"use client"

import type { ColumnDef } from "@tanstack/react-table"
import {
  Edit,
  Trash,
  UserPlus,
  Loader2,
  Mail,
  Phone,
  MapPin,
  Users,
  Filter,
  AlertCircle,
  Search,
  MoreHorizontal,
} from "lucide-react"
import { useSearchParams } from "next/navigation"
import { useState, useEffect } from "react"


import { ClubLayout } from "@/components/layout/club-layout"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable } from "@/components/ui/data-table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { getClubProfileById, getTeams, getStaff, deleteStaff } from "@/lib/services/api"

import type { Staff, Team } from "@/lib/types"

export default function StaffPage() {
  const searchParams = useSearchParams()
  const clubMasterId = Number(searchParams.get("club_master_id")) || 1

  const [clubName, setClubName] = useState<string>("Loading...")
  const [staff, setStaff] = useState<Staff[]>([])
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTeam, setSelectedTeam] = useState<string>("0") // 0 = all, 1 = other, >1 = specific team
  const [editingStaff, setEditingStaff] = useState<Staff | null>(null)
  const [deletingStaff, setDeletingStaff] = useState<Staff | null>(null)
  const { toast } = useToast()

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true)
        const [clubData, teamsData] = await Promise.all([getClubProfileById(clubMasterId), getTeams(clubMasterId)])

        setClubName(clubData.name)
        setTeams([
          { id: 0, name: "All Teams", usav_code: "ALL" },
          { id: 1, name: "Other", usav_code: "OTHER" },
          ...teamsData,
        ])
      } catch (err) {
        setError("Failed to load initial data")
        console.error("Error fetching initial data:", err)
        toast({
          title: "Error",
          description: "Failed to load initial data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchInitialData()
  }, [clubMasterId, toast])

  // Fetch staff data when team filter changes
  useEffect(() => {
    const fetchStaff = async () => {
      try {
        setLoading(true)
        const staffData = await getStaff(clubMasterId, selectedTeam !== "0" ? Number(selectedTeam) : undefined)
        setStaff(staffData)
      } catch (err) {
        setError("Failed to load staff data")
        console.error("Error fetching staff:", err)
        toast({
          title: "Error",
          description: "Failed to load staff data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (clubMasterId) {
      fetchStaff()
    }
  }, [clubMasterId, selectedTeam, toast])

  // Handle staff deletion
  const handleDeleteStaff = (staff: Staff) => {
    setDeletingStaff(staff)
  }

  const handleDeleteConfirm = async () => {
    if (!deletingStaff) return

    try {
      await deleteStaff(deletingStaff.club_staff_master_id)

      toast({
        title: "Staff member deleted",
        description: "The staff member has been removed successfully.",
      })

      // Refresh staff list
      const staffData = await getStaff(clubMasterId, selectedTeam !== "0" ? Number(selectedTeam) : undefined)
      setStaff(staffData)
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to delete staff member.",
        variant: "destructive",
      })
      console.error(err)
    } finally {
      setDeletingStaff(null)
    }
  }

  // Handle staff editing
  const handleEditStaff = (staff: Staff) => {
    setEditingStaff(staff)
  }

  // Handle new staff
  const handleNewStaff = () => {
    // Create empty staff object for new staff
    const newStaff: Staff = {
      club_staff_master_id: 0,
      first: "",
      last: "",
      name: "",
      email: "",
      phone: "",
      phoneh: "",
      phonec: "",
      address: "",
      haddress1: "",
      hcity: "",
      hstate: "",
      roles_id: null,
      club_master_id: clubMasterId,
      team_master_id: null,
      club_name: clubName,
      team_name: "",
      role: "",
      roles: [],
    }
    setEditingStaff(newStaff)
  }

  // Format phone numbers like PHP
  const formatPhones = (phoneh: string | null, phonec: string | null) => {
    let phones = ""
    if (phoneh && phoneh !== "NULL") {
      phones += `${formatPhone(phoneh)}(H)\n`
    }
    if (phonec && phonec !== "NULL") {
      phones += `${formatPhone(phonec)}(C)\n`
    }
    return phones || "N/A"
  }

  const formatPhone = (phone: string | null) => {
    if (!phone || phone === "NULL" || phone === "N/A") return ""
    const cleaned = phone.replace(/\D/g, "")
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
    }
    return phone
  }

  // Define columns for the data table (matching PHP structure)
  const columns: ColumnDef<Staff>[] = [
    {
      id: "actions",
      header: () => (
          <div className="flex items-center gap-2 font-semibold text-blue-700">
            <Edit className="size-4" />
            Actions
          </div>
      ),
      cell: ({ row }) => {
        const staff = row.original
        return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="size-8 p-0">
                  <MoreHorizontal className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleEditStaff(staff)}>
                  <Edit className="mr-2 size-4 text-blue-600" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDeleteStaff(staff)} className="text-red-600">
                  <Trash className="mr-2 size-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
        )
      },
    },
    {
      accessorKey: "first",
      header: () => <div className="font-semibold text-blue-700">First</div>,
      cell: ({ row }) => (
          <Button
              variant="link"
              className="h-auto p-0 font-medium text-blue-600 hover:text-blue-800"
              onClick={() => handleEditStaff(row.original)}
          >
            {row.getValue("first") || "N/A"}
          </Button>
      ),
    },
    {
      accessorKey: "last",
      header: () => <div className="font-semibold text-blue-700">Last</div>,
      cell: ({ row }) => (
          <Button
              variant="link"
              className="h-auto p-0 font-medium text-blue-600 hover:text-blue-800"
              onClick={() => handleEditStaff(row.original)}
          >
            {row.getValue("last") || "N/A"}
          </Button>
      ),
    },
    {
      accessorKey: "role",
      header: () => <div className="font-semibold text-blue-700">Roles</div>,
      cell: ({ row }) => {
        const roles = row.getValue("role") as string
        return roles && roles !== "N/A" ? (
            <div className="whitespace-pre-line text-sm">
              {roles.split("\n").map((role, index) => (
                  <div key={index}>
                    {role && (
                        <Badge variant="secondary" className="mb-1 bg-blue-100 text-blue-800 hover:bg-blue-200">
                          {role}
                        </Badge>
                    )}
                  </div>
              ))}
            </div>
        ) : (
            <span className="text-gray-500">N/A</span>
        )
      },
    },
    {
      accessorKey: "email",
      header: () => (
          <div className="flex items-center gap-2 font-semibold text-blue-700">
            <Mail className="size-4" />
            E-mail
          </div>
      ),
      cell: ({ row }) => {
        const email = row.getValue("email") as string
        return email ? (
            <div className="flex items-center gap-2">
              <Mail className="size-4 text-blue-500" />
              <a href={`mailto:${email}`} className="text-blue-600 transition-colors hover:text-blue-800 hover:underline">
                {email}
              </a>
            </div>
        ) : (
            <span className="text-gray-500">N/A</span>
        )
      },
    },
    {
      accessorKey: "phone",
      header: () => (
          <div className="flex items-center gap-2 font-semibold text-blue-700">
            <Phone className="size-4" />
            Phones
          </div>
      ),
      cell: ({ row }) => {
        const staff = row.original
        const phones = formatPhones(staff.phoneh, staff.phonec)
        return phones !== "N/A" ? (
            <div className="flex items-center gap-2">
              <Phone className="size-4 text-orange-500" />
              <div className="whitespace-pre-line text-sm">{phones}</div>
            </div>
        ) : (
            <span className="text-gray-500">N/A</span>
        )
      },
    },
    {
      accessorKey: "address",
      header: () => (
          <div className="flex items-center gap-2 font-semibold text-blue-700">
            <MapPin className="size-4" />
            Address
          </div>
      ),
      cell: ({ row }) => {
        const staff = row.original
        const address = staff.address
        return address ? (
            <div className="flex items-center gap-2">
              <MapPin className="size-4 text-orange-500" />
              <a
                  href={`https://maps.google.com/?q=${encodeURIComponent(address + " (" + staff.first + " " + staff.last + ")")}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="max-w-xs truncate text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  title={address}
              >
                {address}
              </a>
            </div>
        ) : (
            <span className="text-gray-500">N/A</span>
        )
      },
    },
  ]

  if (loading && staff.length === 0) {
    return (
        <ClubLayout showTabs activeTab="staff">
          <div className="space-y-6">
            <Card className="border-blue-200">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-orange-50">
                <div className="flex items-center gap-3">
                  <div className="size-8 animate-pulse rounded bg-blue-200" />
                  <div className="space-y-2">
                    <div className="h-6 w-48 animate-pulse rounded bg-blue-200" />
                    <div className="h-4 w-32 animate-pulse rounded bg-orange-200" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex h-[50vh] items-center justify-center">
                  <div className="space-y-4 text-center">
                    <Loader2 className="mx-auto size-12 animate-spin text-blue-600" />
                    <div className="space-y-2">
                      <p className="text-lg font-medium text-blue-700">Loading Staff Data</p>
                      <p className="text-sm text-gray-600">Please wait while we fetch the staff information...</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </ClubLayout>
    )
  }

  if (error) {
    return (
        <ClubLayout showTabs activeTab="staff">
          <div className="space-y-6">
            <Card className="border-red-200">
              <CardContent className="p-6">
                <Alert variant="destructive">
                  <AlertCircle className="size-4" />
                  <AlertDescription className="ml-2">{error}</AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </ClubLayout>
    )
  }

  return (
      <ClubLayout showTabs activeTab="staff">
        <div className="space-y-6">
          {/* Header Card */}
          <Card className="border-blue-200 shadow-sm">
            <CardHeader className="border-b border-blue-100 bg-gradient-to-r from-blue-50 to-orange-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-blue-600 p-2">
                    <Users className="size-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-blue-900">{clubName} : Staff</CardTitle>
                    <CardDescription className="text-blue-700">
                      Manage your club&apos;s staff members and their roles
                    </CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="border-orange-300 bg-orange-50 text-orange-700">
                  {staff.length} Total Staff
                </Badge>
              </div>
            </CardHeader>
          </Card>

          {/* Filters Card */}
          <Card className="border-blue-200 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-2">
                <Filter className="size-5 text-blue-600" />
                <CardTitle className="text-lg text-blue-900">Filters</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Users className="size-4 text-orange-600" />
                  <span className="text-sm font-medium text-gray-700">Team:</span>
                  <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                    <SelectTrigger className="w-[200px] border-blue-200 focus:border-blue-400 focus:ring-blue-400">
                      <SelectValue placeholder="Select Team..." />
                    </SelectTrigger>
                    <SelectContent>
                      {teams.map((team) => (
                          <SelectItem key={team.id} value={team.id.toString()} className="focus:bg-blue-50">
                            <div className="flex items-center gap-2">
                              {team.id === 0 ? (
                                  <Users className="size-4 text-blue-600" />
                              ) : team.id === 1 ? (
                                  <div className="size-2 rounded-full bg-gray-500" />
                              ) : (
                                  <div className="size-2 rounded-full bg-orange-500" />
                              )}
                              {team.name}{" "}
                              {team.usav_code &&
                                  team.usav_code !== "ALL" &&
                                  team.usav_code !== "OTHER" &&
                                  `(${team.usav_code})`}
                            </div>
                          </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="bg-blue-100" />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Search className="size-4 text-blue-500" />
                  Showing{" "}
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {staff.length}
                  </Badge>{" "}
                  staff members
                </div>

                <Button className="bg-blue-600 text-white shadow-sm hover:bg-blue-700" onClick={handleNewStaff}>
                  <UserPlus className="mr-2 size-4" />
                  New Staff
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Data Table Card */}
          <Card className="border-blue-200 shadow-sm">
            <CardContent className="p-0">
              <div className="overflow-hidden rounded-lg">
                <DataTable columns={columns} data={staff} className="border-0" />
              </div>
            </CardContent>
          </Card>

          {/* Empty State */}
          {staff.length === 0 && !loading && (
              <Card className="border-orange-200 shadow-sm">
                <CardContent className="p-12 text-center">
                  <div className="space-y-4">
                    <div className="mx-auto w-fit rounded-full bg-orange-100 p-4">
                      <Users className="size-8 text-orange-600" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-gray-900">No Staff Members Found</h3>
                      <p className="text-gray-600">
                        {selectedTeam === "0"
                            ? "There are no staff members in this club yet."
                            : selectedTeam === "1"
                                ? "No staff members found in the &apos;Other&apos; category."
                                : "No staff members found for the selected team."}
                      </p>
                    </div>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700" onClick={handleNewStaff}>
                      <UserPlus className="mr-2 size-4" />
                      Add First Staff Member
                    </Button>
                  </div>
                </CardContent>
              </Card>
          )}

          {/* Delete Confirmation Modal */}
          {deletingStaff && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <Card className="mx-4 w-full max-w-md">
                  <CardHeader>
                    <CardTitle className="text-red-600">Delete Staff Member</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-700">
                      Are you sure you want to delete &quot;{deletingStaff.first} {deletingStaff.last}&quot;? This action cannot be
                      undone.
                    </p>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setDeletingStaff(null)}>
                        Cancel
                      </Button>
                      <Button variant="destructive" onClick={handleDeleteConfirm}>
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
          )}

          {/* Edit Staff Modal */}
          {editingStaff && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <Card className="mx-4 w-full max-w-2xl">
                  <CardHeader>
                    <CardTitle>
                      {editingStaff.club_staff_master_id === 0
                          ? "Add New Staff"
                          : `Edit Staff: ${editingStaff.first} ${editingStaff.last}`}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">Staff edit modal will be implemented here...</p>
                    <div className="mt-4 flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setEditingStaff(null)}>
                        Cancel
                      </Button>
                      <Button className="bg-blue-600 hover:bg-blue-700">Save</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
          )}
        </div>
      </ClubLayout>
  )
}
