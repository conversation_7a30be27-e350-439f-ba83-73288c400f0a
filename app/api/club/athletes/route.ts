import { type NextRequest, NextResponse } from "next/server"

import prisma from "@/lib/prisma"
import { handleApiError, ApiError } from "@/lib/services/errorHandler"
import type { Athlete, CreateAthleteData } from "@/lib/types/athlete"
import { formatHeight, formatName } from "@/lib/utils/formatUtils"
import { athleteSchema } from "@/lib/validations/athlete"

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const searchParams = request.nextUrl.searchParams
    const clubId = searchParams.get("club_id") ? Number.parseInt(searchParams.get("club_id")!) : 1
    const teamId = searchParams.get("team_id") ? Number.parseInt(searchParams.get("team_id")!) : null
    const search = searchParams.get("search") || ""

    if (isNaN(clubId)) {
      throw new ApiError("Invalid club ID", 400)
    }

    const whereClause: any = {
      club_master_id: clubId,
    }

    // Add team filter if provided
    if (teamId && !isNaN(teamId)) {
      whereClause.team_master_id = teamId
    }

    // Add search filter if provided (case-insensitive)
    if (search) {
      whereClause.OR = [
        { first: { contains: search, mode: "insensitive" } },
        { last: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ]
    }

    const athletes = await prisma.athlete_master.findMany({
      where: whereClause,
      orderBy: [{ last: "asc" }, { first: "asc" }],
    })

    // Fetch club data separately since relation doesn't exist
    const clubData = await prisma.club_master.findUnique({
      where: { club_master_id: clubId },
      select: { club_name: true }
    })
    const clubName = clubData?.club_name || "Unknown Club"

    // Get unique team IDs and fetch team data
    const teamIds = [...new Set(athletes.map(a => a.team_master_id).filter(Boolean))]
    const teams = teamIds.length > 0 ? await prisma.team_master.findMany({
      where: { team_master_id: { in: teamIds } },
      select: { team_master_id: true, team_name: true }
    }) : []

    const teamMap = new Map(teams.map(t => [t.team_master_id, t.team_name]))

    const formattedAthletes = athletes.map((athlete) => ({
      ...athlete,
      athlete_master_id: athlete.athlete_master_id,
      first: athlete.first || "",
      last: athlete.last || "",
      name: formatName(athlete.first, athlete.last),
      email: athlete.email || "",
      gradyear: athlete.gradyear || null,
      phoneh: athlete.phoneh || "",
      phonec: athlete.phonec || "",
      height: athlete.height || null,
      height_formatted: formatHeight(athlete.height),
      gender: athlete.gender || "",
      high_school: athlete.high_school || "",
      scholarship_status: athlete.scholarship_status || "",
      position1: athlete.position1 || null,
      position2: athlete.position2 || null,
      uniform1: athlete.uniform1 || null,
      uniform2: athlete.uniform2 || null,
      club_master_id: athlete.club_master_id,
      team_master_id: athlete.team_master_id,
      team_name: teamMap.get(athlete.team_master_id || 0) || "NO TEAM",
      club_name: clubName,
      gpa: athlete.gpa ? Number(athlete.gpa) : null,
    }))

    return NextResponse.json(formattedAthletes)
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const data = await request.json()
    const validatedData = athleteSchema.parse(data)

    const createData: CreateAthleteData = {
      first: validatedData.first,
      last: validatedData.last,
      email: validatedData.email || null,
      gradyear: validatedData.gradyear || null,
      phoneh: validatedData.phoneh || null,
      phonec: validatedData.phonec || null,
      height: validatedData.height || null,
      gender: validatedData.gender || null,
      high_school: validatedData.high_school || null,
      scholarship_status: validatedData.scholarship_status || null,
      position1: validatedData.position1 || null,
      position2: validatedData.position2 || null,
      uniform1: validatedData.uniform1 || null,
      uniform2: validatedData.uniform2 || null,
      club_master_id: validatedData.club_master_id,
      team_master_id: validatedData.team_master_id || null,
    }

    const athlete = await prisma.athlete_master.create({
      data: createData,
    })

    // Fetch related data separately since relations don't exist
    const clubData = await prisma.club_master.findUnique({
      where: { club_master_id: athlete.club_master_id },
      select: { club_name: true }
    })
    const clubName = clubData?.club_name || "Unknown Club"

    const teamData = athlete.team_master_id ? await prisma.team_master.findUnique({
      where: { team_master_id: athlete.team_master_id },
      select: { team_name: true }
    }) : null
    const teamName = teamData?.team_name || "NO TEAM"

    const formattedAthlete = {
      athlete_master_id: athlete.athlete_master_id,
      first: athlete.first || "",
      last: athlete.last || "",
      name: formatName(athlete.first, athlete.last),
      email: athlete.email || "",
      gradyear: athlete.gradyear || null,
      phoneh: athlete.phoneh || "",
      phonec: athlete.phonec || "",
      height: athlete.height || null,
      height_formatted: formatHeight(athlete.height),
      gender: athlete.gender || "",
      high_school: athlete.high_school || "",
      scholarship_status: athlete.scholarship_status || "",
      position1: athlete.position1 || null,
      position2: athlete.position2 || null,
      uniform1: athlete.uniform1 || null,
      uniform2: athlete.uniform2 || null,
      club_master_id: athlete.club_master_id,
      team_master_id: athlete.team_master_id,
      team_name: teamName,
      club_name: clubName,
      gpa: athlete.gpa ? Number(athlete.gpa) : null,
    }

    return NextResponse.json(formattedAthlete, { status: 201 })
  } catch (error) {
    return handleApiError(error)
  }
}
