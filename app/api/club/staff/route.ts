import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { staffSchema } from "@/lib/validations/staff"
import { handleApiError, ApiError } from "@/lib/services/errorHandler"
import { formatName } from "@/lib/utils/formatUtils"
import type { Staff, CreateStaffData } from "@/lib/types/staff"

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const searchParams = request.nextUrl.searchParams
    const clubId = Number.parseInt(searchParams.get("club_id")!)
    const teamId = Number.parseInt(searchParams.get("team_id")!)

    if (isNaN(clubId)) {
      throw new ApiError("Invalid club ID", 400)
    }

    let staff: any[] = []

    if (teamId > 1) {
      // Get staff for specific team using roles table
      staff = await prisma.club_staff_master.findMany({
        where: {
          club_master_id: clubId,
          club_staff_master_role: {
            some: {
              team_master_id: teamId,
            },
          },
        },
        include: {
          club_staff_master_role: {
            include: {
              roles: {
                select: {
                  role: true,
                },
              },
              team_master: {
                select: {
                  team_name: true,
                },
              },
            },
          },
        },
        orderBy: [{ last: "asc" }, { first: "asc" }],
      })
    } else if (teamId === 1) {
      // "Other" team - show staff without team roles and club directors/coordinators
      staff = await prisma.club_staff_master.findMany({
        where: {
          club_master_id: clubId,
          OR: [
            {
              club_staff_master_role: {
                none: {},
              },
            },
            {
              club_staff_master_role: {
                some: {
                  roles_id: {
                    in: [11, 16], // REC.COORD and CLUB_DIRECTOR
                  },
                },
              },
            },
          ],
        },
        include: {
          club_staff_master_role: {
            include: {
              roles: {
                select: {
                  role: true,
                },
              },
              team_master: {
                select: {
                  team_name: true,
                },
              },
            },
          },
        },
        orderBy: [{ last: "asc" }, { first: "asc" }],
      })
    } else {
      // All staff - using raw query to handle master_row = club_staff_master_id condition
      staff = await prisma.$queryRaw`
        SELECT csm.*,
               GROUP_CONCAT(DISTINCT CONCAT(r.role, '|', COALESCE(tm.team_name, ''), '|', csmr.roles_id, '|', COALESCE(csmr.team_master_id, '')) SEPARATOR '||') as roles_data
        FROM club_staff_master csm
        LEFT JOIN club_staff_master_role csmr ON csm.club_staff_master_id = csmr.club_staff_master_id
        LEFT JOIN roles r ON csmr.roles_id = r.roles_id
        LEFT JOIN team_master tm ON csmr.team_master_id = tm.team_master_id
        WHERE csm.club_master_id = ${clubId}
          AND csm.master_row = csm.club_staff_master_id
        GROUP BY csm.club_staff_master_id
        ORDER BY csm.last ASC, csm.first ASC
      `
    }

    // Fetch club name separately since relation doesn't exist
    const clubData = await prisma.club_master.findUnique({
      where: { club_master_id: clubId },
      select: { club_name: true }
    })
    const clubName = clubData?.club_name || "Unknown Club"

    const formattedStaff: Staff[] = staff.map((member) => {
      // Build roles list like PHP
      let roleList = ""
      if (member.club_staff_master_role && member.club_staff_master_role.length > 0) {
        member.club_staff_master_role.forEach((role: any) => {
          roleList += role.roles?.role || "Unknown Role"
          if (role.team_master_id > 1 && role.roles_id !== 11 && role.roles_id !== 16) {
            roleList += ` => ${role.team_master?.team_name || "Unknown Team"}\n`
          } else {
            roleList += "\n"
          }
        })
      } else if (member.roles_data) {
        // Handle raw query result format
        const rolesArray = member.roles_data.split('||')
        rolesArray.forEach((roleData: string) => {
          const [role, teamName, rolesId, teamMasterId] = roleData.split('|')
          roleList += role || "Unknown Role"
          if (parseInt(teamMasterId) > 1 && parseInt(rolesId) !== 11 && parseInt(rolesId) !== 16) {
            roleList += ` => ${teamName || "Unknown Team"}\n`
          } else {
            roleList += "\n"
          }
        })
      } else {
        roleList = "N/A"
      }

      // Format phones like PHP
      let phones = ""
      if (member.phoneh && member.phoneh !== "NULL") {
        phones += `${formatPhone(member.phoneh)}(H)\n`
      }
      if (member.phonec && member.phonec !== "NULL") {
        phones += `${formatPhone(member.phonec)}(C)\n`
      }
      if (!phones) {
        phones = "N/A"
      }

      return {
        club_staff_master_id: member.club_staff_master_id,
        first: member.first || "",
        last: member.last || "",
        name: formatName(member.first, member.last),
        email: member.email || "",
        phone: phones,
        phoneh: member.phoneh || "",
        phonec: member.phonec || "",
        address:
            `${member.haddress1 || ""}, ${member.hcity || ""}, ${member.hstate || ""}`.replace(/^,\s*|,\s*$/g, "") || "",
        haddress1: member.haddress1 || "",
        hcity: member.hcity || "",
        hstate: member.hstate || "",
        roles_id: null,
        club_master_id: member.club_master_id,
        team_master_id: null,
        club_name: clubName,
        team_name: "N/A",
        role: roleList.trim(),
        roles: member.club_staff_master_role || (member.roles_data ?
          member.roles_data.split('||').map((roleData: string) => {
            const [role, teamName, rolesId, teamMasterId] = roleData.split('|')
            return {
              club_staff_master_role_id: 0,
              club_staff_master_id: member.club_staff_master_id,
              roles_id: parseInt(rolesId) || 0,
              team_master_id: parseInt(teamMasterId) || null,
              roles: { role: role || "Unknown Role" },
              team_master: { team_name: teamName || "Unknown Team" }
            }
          }) : []),
      }
    })

    return NextResponse.json(formattedStaff)
  } catch (error) {
    return handleApiError(error)
  }
}

// Helper function to format phone numbers like PHP
function formatPhone(phone: string | null): string {
  if (!phone || phone === "NULL" || phone === "N/A") return ""

  const cleaned = phone.replace(/\D/g, "")
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  }
  return phone
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const data = await request.json()
    const validatedData = staffSchema.parse(data)

    const createData: CreateStaffData = {
      first: validatedData.first,
      last: validatedData.last,
      email: validatedData.email || null,
      phonec: validatedData.phonec || null,
      phoneh: validatedData.phoneh || null,
      haddress1: validatedData.haddress1 || null,
      hcity: validatedData.hcity || null,
      hstate: validatedData.hstate || null,
      club_master_id: validatedData.club_master_id,
      master_row: 0, // Will be updated after insert
    }

    const staffMember = await prisma.club_staff_master.create({
      data: createData,
    })

    // Update master_row to point to itself (like PHP logic)
    await prisma.club_staff_master.update({
      where: { club_staff_master_id: staffMember.club_staff_master_id },
      data: { master_row: staffMember.club_staff_master_id },
    })

    // Save staff roles if provided
    if (data.staffRoles && Array.isArray(data.staffRoles)) {
      for (const role of data.staffRoles) {
        await prisma.club_staff_master_role.create({
          data: {
            club_staff_master_id: staffMember.club_staff_master_id,
            roles_id: role.roles_id,
            team_master_id: role.team_master_id || null,
          },
        })
      }
    }

    return NextResponse.json(staffMember, { status: 201 })
  } catch (error) {
    return handleApiError(error)
  }
}
