import { Prisma } from "@prisma/client"
import { type NextRequest, NextResponse } from "next/server"

import prisma from "@/lib/prisma"
import { handleApiError, ApiError } from "@/lib/services/errorHandler"
import type { Team, CreateTeamData } from "@/lib/types/team"
import { teamSchema } from "@/lib/validations/team"

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const searchParams = request.nextUrl.searchParams
    const clubId = Number.parseInt(searchParams.get("club_id")!)

    if (isNaN(clubId)) {
      throw new ApiError("Invalid club ID", 400)
    }

    const teams = await prisma.team_master.findMany({
      where: { club_id: clubId },
      include: {
        _count: {
          select: {
            athlete_users: true,
            club_staff_master_role: true,
            club_staff_roster: true,
          },
        },
      },
      orderBy: [{ age: "asc" }, { team_name: "asc" }],
    })

    // Fetch club data separately since relation doesn't exist
    const clubData = await prisma.club_master.findUnique({
      where: { club_master_id: clubId },
      select: { club_name: true }
    })
    const clubName = clubData?.club_name || "Unknown Club"

    // Get athlete counts for each team using raw query since relation doesn't exist
    let athleteCountMap = new Map<number, number>()

    if (teams.length > 0) {
      const teamIds = teams.map(t => t.team_master_id)
      const athleteCounts = await prisma.$queryRaw<Array<{team_master_id: number, athlete_count: number}>>`
        SELECT team_master_id, COUNT(*) as athlete_count
        FROM athlete_team
        WHERE team_master_id IN (${Prisma.join(teamIds)})
        GROUP BY team_master_id
      `

      athleteCountMap = new Map(athleteCounts.map(ac => [ac.team_master_id, Number(ac.athlete_count)]))
    }

    const formattedTeams: Team[] = teams.map((team) => ({
      id: team.team_master_id,
      name: team.team_name,
      usav_code: team.organization_code || null,
      division: team.division || null,
      age: team.age || null,
      rank: team.rank ? Number.parseInt(team.rank) : null,
      club_id: team.club_id,
      athlete_count: athleteCountMap.get(team.team_master_id) || 0,
      club_name: clubName,
      created_at: team.date_modified?.toISOString() || new Date().toISOString(),
    }))

    return NextResponse.json(formattedTeams)
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const data = await request.json()
    const validatedData = teamSchema.parse(data)

    const createData: CreateTeamData = {
      team_name: validatedData.name,
      organization_code: validatedData.usav_code || null,
      division: validatedData.division || null,
      age: validatedData.age,
      rank: validatedData.rank?.toString() || null,
      club_id: validatedData.club_id,
      date_modified: new Date(),
    }

    const team = await prisma.team_master.create({
      data: createData,
      include: {
        _count: {
          select: {
            athlete_users: true,
            club_staff_master_role: true,
            club_staff_roster: true,
          },
        },
      },
    })

    // Fetch club data separately since relation doesn't exist
    const clubData = await prisma.club_master.findUnique({
      where: { club_master_id: team.club_id },
      select: { club_name: true }
    })
    const clubName = clubData?.club_name || "Unknown Club"

    const formattedTeam: Team = {
      id: team.team_master_id,
      name: team.team_name,
      usav_code: team.organization_code || null,
      division: team.division || null,
      age: team.age || null,
      rank: team.rank ? Number.parseInt(team.rank) : null,
      club_id: team.club_id,
      athlete_count: 0, // New team, no athletes yet
      club_name: clubName,
      created_at: team.date_modified?.toISOString() || new Date().toISOString(),
    }

    return NextResponse.json(formattedTeam, { status: 201 })
  } catch (error) {
    return handleApiError(error)
  }
}
