import { API_ENDPOINTS } from "@/lib/constants/apiEndpoints"

import { ApiError } from "./errorHandler"

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(endpoint, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError(errorData.error || `HTTP ${response.status}: ${response.statusText}`, response.status)
      }

      return await response.json()
    } catch (error) {
      if (error instanceof ApiError) throw error
      throw new ApiError("Network error occurred", 500)
    }
  }

  // Club methods
  async getClubProfileById(clubId: number) {
    const params = new URLSearchParams({ club_id: clubId.toString() })
    return this.request(`${API_ENDPOINTS.CLUB_PROFILE}?${params}`)
  }

  async updateClubProfile (clubProfileData: any){
    return this.request(API_ENDPOINTS.CLUB_PROFILE, {
      method: "PUT",
      body: JSON.stringify(clubProfileData),
    })
  }

  // Athletes methods
  async getAthletes(
      clubId: number,
      filters: {
        teamId?: number
        lastName?: string
        gradYear?: number
        tournamentId?: number
      } = {},
  ) {
    const params = new URLSearchParams({
      club_id: clubId.toString(),
      ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== undefined && value !== "")),
    })

    const athletes: [] = await this.request(`${API_ENDPOINTS.CLUB_ATHLETES}?${params}`)

    return athletes.map((athlete: any) => ({
      ...athlete,
      team_name: athlete.team_name,
      name: [athlete.first, athlete.last].filter(Boolean).join(" "),
      height_formatted: athlete.height
          ? `${Math.floor(athlete.height / 12)}'${(athlete.height % 12).toString().padStart(2, "0")}"`
          : "N/A",
      phoneh_formatted: athlete.phoneh || "N/A",
      phonec_formatted: athlete.phonec || "N/A",
      uniform_number: athlete.uniform1 ? athlete.uniform1.toString() : "N/A",
    }))
  }

  async getAthleteById(athleteId: number) {
    return this.request(API_ENDPOINTS.ATHLETE_BY_ID(athleteId))
  }

  async createAthlete(athleteData: any) {
    return this.request(API_ENDPOINTS.CLUB_ATHLETES, {
      method: "POST",
      body: JSON.stringify(athleteData),
    })
  }

  async updateAthlete(athleteId: number, athleteData: any) {
    return this.request(API_ENDPOINTS.ATHLETE_BY_ID(athleteId), {
      method: "PUT",
      body: JSON.stringify(athleteData),
    })
  }

  async deleteAthlete(athleteId: number) {
    return this.request(API_ENDPOINTS.ATHLETE_BY_ID(athleteId), {
      method: "DELETE",
    })
  }

  // Teams methods
  async getTeams(clubId: number) {
    const params = new URLSearchParams({ club_id: clubId.toString() })
    return this.request(`${API_ENDPOINTS.CLUB_TEAMS}?${params}`)
  }

  async getTeamById(teamId: number) {
    return this.request(API_ENDPOINTS.TEAM_BY_ID(teamId))
  }

  async createTeam(teamData: any) {
    return this.request(API_ENDPOINTS.CLUB_TEAMS, {
      method: "POST",
      body: JSON.stringify(teamData),
    })
  }

  async updateTeam(teamId: number, teamData: any) {
    return this.request(API_ENDPOINTS.TEAM_BY_ID(teamId), {
      method: "PUT",
      body: JSON.stringify(teamData),
    })
  }

  async deleteTeam(teamId: number) {
    return this.request(API_ENDPOINTS.TEAM_BY_ID(teamId), {
      method: "DELETE",
    })
  }

  // Staff methods
  async getStaff(clubId: number, teamId?: number) {
    const params = new URLSearchParams({ club_id: clubId.toString() })
    if (teamId) params.set("team_id", teamId.toString())

    return this.request(`${API_ENDPOINTS.CLUB_STAFF}?${params}`)
  }

  async createStaff(staffData: any) {
    return this.request(API_ENDPOINTS.CLUB_STAFF, {
      method: "POST",
      body: JSON.stringify(staffData),
    })
  }

  async deleteStaff(staffId: number) {
    return this.request(API_ENDPOINTS.STAFF_BY_ID(staffId), {
      method: "DELETE",
    })
  }

  // Tournaments methods
  async getTournaments() {
    return this.request(API_ENDPOINTS.CLUB_TOURNAMENTS)
  }

  async getUserByClubId(clubId: number) {
    const params = new URLSearchParams({ club_id: clubId.toString() })
    return this.request(`/api/club/user?${params}`)
  }

  async updateUser(userData: any) {
    return this.request('/api/club/users', {
      method: "PUT",
      body: JSON.stringify(userData),
    })
  }

  async getAthleteRosters(athleteId: number) {
    return this.request(`/api/club/athletes/${athleteId}/rosters`)
  }

  async getAthleteHistory(athleteId: number) {
    return this.request(`/api/club/athletes/${athleteId}/history`)
  }
}

// Create a singleton instance
const apiService = new ApiService()

// Export individual methods for backward compatibility
export const getClubProfileById = (clubId: number) => apiService.getClubProfileById(clubId)
export const updateClubProfile = (clubProfileData: any) => apiService.updateClubProfile(clubProfileData)
export const getAthletes = (clubId: number, filters?: any) => apiService.getAthletes(clubId, filters)
export const getAthleteById = (athleteId: number) => apiService.getAthleteById(athleteId)
export const createAthlete = (athleteData: any) => apiService.createAthlete(athleteData)
export const updateAthlete = (athleteId: number, athleteData: any) => apiService.updateAthlete(athleteId, athleteData)
export const deleteAthlete = (athleteId: number) => apiService.deleteAthlete(athleteId)
export const getTeams = (clubId: number) => apiService.getTeams(clubId)
export const getTeamById = (teamId: number) => apiService.getTeamById(teamId)
export const createTeam = (teamData: any) => apiService.createTeam(teamData)
export const updateTeam = (teamId: number, teamData: any) => apiService.updateTeam(teamId, teamData)
export const deleteTeam = (teamId: number) => apiService.deleteTeam(teamId)
export const getStaff = (clubId: number, teamId?: number) => apiService.getStaff(clubId, teamId)
export const createStaff = (staffData: any) => apiService.createStaff(staffData)
export const deleteStaff = (staffId: number) => apiService.deleteStaff(staffId)
export const getTournaments = () => apiService.getTournaments()
export const getUserByClubId = (clubId: number) => apiService.getUserByClubId(clubId)
export const updateUser = (userData: any) => apiService.updateUser(userData)
export const getAthleteRosters = (athleteId: number) => apiService.getAthleteRosters(athleteId)
export const getAthleteHistory = (athleteId: number) => apiService.getAthleteHistory(athleteId)

export default apiService
