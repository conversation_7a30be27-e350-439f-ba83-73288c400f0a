/**
 * Athlete-related types
 */

export interface Athlete {
  athlete_master_id: number
  first: string
  last: string
  email: string | null
  gradyear: number | null
  phoneh: string | null
  phonec: string | null
  height: number | null
  gender: string | null
  high_school: string | null
  high_school_address: string | null
  scholarship_status: string | null
  haddress1: string | null
  haddress2: string | null
  hcity: string | null
  hstate: string | null
  hzip: string | null
  gpa: number | null
  sat_total: number | null
  position1: number | null
  position2: number | null
  uniform1: number | null
  uniform2: number | null
  college_name: string | null
  parent1_name: string | null
  parent1_email: string | null
  parent1_phonec: string | null
  parent2_name: string | null
  parent2_email: string | null
  parent2_phonec: string | null
  team_master_id: number | null
  club_master_id: number
  date_modified: Date
  date_created: Date
}

export interface AthleteUser {
  athlete_user_id: number
  athlete_master_id: number
  users_id: number | null
  nick: string | null
  weight: number | null
  reach: number | null
  approach: number | null
  block: number | null
  handed: string | null
  act: number | null
  satcr: number | null
  satm: number | null
  satw: number | null
  birthdate: Date | null
  gpa: number | null
  gpa_scale: number | null
  high_school_city: string | null
  high_school_state: string | null
  high_school_zip: string | null
  club_coach_name: string | null
  club_coach_email: string | null
  club_coach_phone: string | null
  club_director_name: string | null
  club_director_email: string | null
  club_director_phone: string | null
  photo_links: string | null
  video_links: string | null
  blog_links: string | null
  link_facebook: string | null
  link_instagram: string | null
  link_twitter: string | null
  link_snapchat: string | null
  link_recruiting_service: string | null
  link_maxpreps: string | null
  coordinator_name: string | null
  coordinator_email: string | null
  allow_camp_emails: boolean
  audition_video_5: string | null
  audition_video_30: string | null
  psat: number | null
}

export interface AthleteWithRelations extends Athlete {
  athlete_user?: AthleteUser | null
  club_master?: {
    club_name: string
    club_master_id: number
  } | null
  team_master?: {
    team_name: string
    id: number
  } | null
  users?: {
    portrait: string | null
    email_alternate: string | null
    personal: string | null
  } | null
}

export interface FormattedAthlete {
  athlete_master_id: number
  first: string
  last: string
  name: string
  email: string
  gradyear: number | null
  phoneh: string
  phonec: string
  height: number | null
  gender: string
  high_school: string
  high_school_address: string
  scholarship_status: string
  haddress1: string
  haddress2: string
  hcity: string
  hstate: string
  hzip: string
  gpa: number | null
  act: number | null
  sat_total: number | null
  satcr: number | null
  satm: number | null
  satw: number | null
  position1: number | null
  position2: number | null
  uniform1: number | null
  uniform2: number | null
  birthdate: string | null
  college_name: string
  weight: number | null
  reach: number | null
  approach: number | null
  block: number | null
  handed: string | null
  nick: string | null
  parent1_name: string
  parent1_email: string
  parent1_phonec: string
  parent2_name: string
  parent2_email: string
  parent2_phonec: string
  club: {
    club_master_id: number | null
    club_name: string
  }
  team_master_id: number | null
  team_name: string
  teamReprName: string | null
  teamReprEmail: string | null
  portrait: string
  users_id: number | null
  personal: string | null
  high_school_city: string | null
  high_school_state: string | null
  high_school_zip: string | null
  gpa_scale: number | null
  club_coach_name: string | null
  club_coach_email: string | null
  club_coach_phone: string | null
  club_director_name: string | null
  club_director_email: string | null
  club_director_phone: string | null
  photo_links: string | null
  video_links: string | null
  blog_links: string | null
  link_facebook: string | null
  link_instagram: string | null
  link_twitter: string | null
  link_snapchat: string | null
  link_recruiting_service: string | null
  link_maxpreps: string | null
  coordinator_name: string | null
  coordinator_email: string | null
  allow_camp_emails: boolean
  audition_video_5: string | null
  audition_video_30: string | null
  psat: number | null

  // Formatted fields
  height_formatted?: string
  phoneh_formatted?: string
  phonec_formatted?: string
}

export interface AthleteChange {
  athlete_master_id: number
  name: string
  value: string
  old_value: string
  author_type: "club" | "athlete" | "admin"
  author_entity_id: number
  status: "new" | "approved" | "declined" | "locked" | "unlocked"
  notes?: string
  date_created?: Date
  date_approved?: Date
}

export interface AthleteRoster {
  athlete_roster_id: number
  tournament_id: number
  first: string
  last: string
  team_roster_id: number
  position: number | null
  position1: number | null
  position2: number | null
  uniform1: string | null
  date_created: number
  tournament_name: string
  club_name: string
  team_name: string
}

export interface AthleteSearchParams {
  club_id: number
  last_name?: string
  team_id?: number
  grad_year?: string
  tournament_id?: string
}

export interface CreateAthleteData {
  first: string
  last: string
  email?: string | null
  gradyear?: number | null
  phoneh?: string | null
  phonec?: string | null
  height?: number | null
  gender?: string | null
  high_school?: string | null
  high_school_address?: string | null
  scholarship_status?: string | null
  haddress1?: string | null
  haddress2?: string | null
  hcity?: string | null
  hstate?: string | null
  hzip?: string | null
  gpa?: number | null
  sat_total?: number | null
  position1?: number | null
  position2?: number | null
  uniform1?: number | null
  uniform2?: number | null
  college_name?: string | null
  parent1_name?: string | null
  parent1_email?: string | null
  parent1_phonec?: string | null
  parent2_name?: string | null
  parent2_email?: string | null
  parent2_phonec?: string | null
  team_master_id?: number | null
  club_master_id: number
}
