generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model sport_position {
  sport_position_id Int      @id @db.SmallInt
  sports_id         Int      @db.SmallInt
  label             String?  @db.VarChar(30)
  abbrev            String?  @default("") @db.VarChar(7)
  aliases           String?  @db.VarChar(125)
  date_modified     DateTime @default(now()) @updatedAt @db.Timestamp(0)
  ncsa_code         String?  @db.VarChar(30)

  @@index([sport_position_id, abbrev], map: "sport_position_and_abbrev")
  @@index([sports_id], map: "sports_id")
  @@index([sports_id], map: "sports_id_2")
  @@index([sports_id], map: "sports_id_idxfk")
}

model club_master {
  club_master_id             Int                 @id @default(autoincrement())
  old_roster_clubs_master_id Int?                @default(0)
  club_name                  String?             @db.VarChar(150)
  region_code                String?             @db.VarChar(5)
  club_code                  String?             @db.VarChar(30)
  address1                   String?             @db.VarChar(50)
  address2                   String?             @db.VarChar(50)
  city                       String?             @db.VarChar(50)
  state                      String?             @db.VarChar(2)
  zip                        String?             @db.VarChar(10)
  url                        String?             @db.VarChar(300)
  office_phone               String?             @db.VarChar(25)
  other_phone                String?             @db.VarChar(25)
  fax                        String?             @db.VarChar(25)
  email                      String?             @db.VarChar(255)
  date_modified              DateTime            @default(now()) @updatedAt @db.Timestamp(0)
  flag                       String?             @db.VarChar(15)
  date_created               Int
  sports_id                  Int                 @default(2) @db.SmallInt
  main_club_staff_master_id  Int?                @default(0)
  active                     YesNo?              @default(y)
  valid_club_master_id       Int?
  active_last_year           YesNo               @default(n)
  hidden                     YesNo               @default(n)
  coordinator_first          String?             @db.VarChar(45)
  coordinator_last           String?             @db.VarChar(45)
  coordinator_email          String?             @db.VarChar(75)
  coordinator_phone          String?             @db.VarChar(25)
  date_admin_modified        DateTime?           @db.Timestamp(0)
  virtual                    YesNo?              @default(n)
  athlete_users              athlete_user[]
  club_staff_roster          club_staff_roster[]

  @@index([city], map: "city")
  @@index([date_modified], map: "dm")
  @@index([email(length: 191)], map: "email")
  @@index([club_name], map: "name")
  @@index([sports_id, active, hidden], map: "sportId_active_hidden")
  @@index([state], map: "state")
  @@index([zip], map: "zip")
}

model club_user {
  club_user_id         Int       @id @default(autoincrement())
  club_staff_master_id Int?
  users_id             Int?
  sports_id            Int?      @db.SmallInt
  roles_id             Int?
  club_master_id       Int?
  team_master_id       Int?
  birthdate            DateTime? @db.Date
  gender               String?   @db.VarChar(1)
  first                String?   @db.VarChar(40)
  nick                 String?   @db.VarChar(40)
  last                 String?   @db.VarChar(40)
  phoneh               String?   @db.VarChar(20)
  phonec               String?   @db.VarChar(20)
  phonew               String?   @db.VarChar(20)
  email                String?   @db.VarChar(255)
  haddress1            String?   @db.VarChar(100)
  haddress2            String?   @db.VarChar(50)
  hcity                String?   @db.VarChar(50)
  hstate               String?   @db.VarChar(2)
  hzip                 String?   @db.VarChar(10)
  waddress1            String?   @db.VarChar(50)
  waddress2            String?   @db.VarChar(50)
  wstate               String?   @db.VarChar(2)
  wcity                String?   @db.VarChar(25)
  wzip                 String?   @db.VarChar(10)
  date_modified        DateTime  @default(now()) @updatedAt @db.Timestamp(0)

  @@unique([club_staff_master_id, club_user_id], map: "old_id_and_csm_id")
  @@index([email(length: 191)], map: "email")
  @@index([roles_id], map: "roles_id")
  @@index([club_master_id], map: "roster_clubs_id")
  @@index([team_master_id], map: "roster_teams_id")
  @@index([last, first, club_master_id], map: "search_by_name_index")
  @@index([sports_id], map: "sports_id")
  @@index([users_id], map: "users")
  @@index([users_id], map: "users_id")
}

model team_master {
  team_master_id             Int                      @id @default(autoincrement())
  old_roster_teams_master_id Int                      @default(0)
  team_roster_id             Int?
  date_modified              DateTime                 @default(now()) @updatedAt @db.Timestamp(0)
  sports_id                  Int                      @default(2) @db.SmallInt
  club_id                    Int                      @default(1) @map("club_master_id")
  organization_code          String?                  @db.VarChar(19)
  team_name                  String?                  @db.VarChar(100)
  schedule_name              String?                  @db.VarChar(50)
  age                        Int                      @db.TinyInt
  rank                       String?                  @db.VarChar(1)
  division                   String?                  @db.VarChar(75)
  bracket                    String?                  @db.VarChar(25)
  pool                       String?                  @db.VarChar(16)
  seed                       Int?                     @db.SmallInt
  placement                  Boolean?
  state                      String?                  @db.VarChar(2)
  zip                        String?                  @db.VarChar(10)
  chapter                    String?                  @db.VarChar(50)
  uni_color                  String?                  @db.VarChar(50)
  uni_reverse                String?                  @default("n") @db.VarChar(1)
  flag                       String?                  @db.MediumText
  deleted                    Boolean                  @default(false)
  date_created               Int
  hidden                     YesNo                    @default(n)
  virtual                    YesNo?                   @default(n)
  athlete_users              athlete_user[]
  club_staff_master_role     club_staff_master_role[]
  club_staff_roster          club_staff_roster[]
  team_roster                team_roster?             @relation(fields: [team_roster_id], references: [team_roster_id], onUpdate: NoAction, map: "fk_team_master_team_roster_id")

  @@index([age], map: "age")
  @@index([club_id], map: "club_master_id")
  @@index([club_id, deleted], map: "club_master_id+deleted")
  @@index([division], map: "division")
  @@index([sports_id], map: "fk_team_master_sports_id")
  @@index([team_roster_id], map: "fk_team_master_team_roster_id")
  @@index([hidden], map: "hidden")
  @@index([team_name], map: "name")
  @@index([organization_code], map: "organization_code_index")
  @@index([state], map: "state")
}

model team_roster {
  team_roster_id            Int                              @id @default(autoincrement())
  date_modified             DateTime                         @default(now()) @updatedAt @db.Timestamp(0)
  team_master_id            Int?                             @default(1)
  old_roster_teams_id       Int                              @default(0)
  tournament_id             Int
  roster_source_id          Int                              @default(0)
  roster_source_provider    String                           @default("") @db.VarChar(4)
  roster_source_club_row_id String                           @db.VarChar(50)
  roster_source_team_row_id String?                          @db.VarChar(50)
  roster_source_last_update DateTime?                        @db.Timestamp(0)
  roster_source_status      team_roster_roster_source_status @default(EMPTY_ENUM_VALUE)
  club_roster_id            Int?
  organization_code         String?                          @db.VarChar(19)
  team_name                 String                           @default("") @db.VarChar(100)
  schedule_name             String?                          @db.VarChar(50)
  age                       Int                              @db.TinyInt
  rank                      String?                          @db.VarChar(1)
  division                  String?                          @db.VarChar(75)
  bracket                   String?                          @db.VarChar(25)
  pool                      String?                          @db.VarChar(16)
  seed                      Int?                             @db.SmallInt
  placement                 Boolean?
  state                     String?                          @db.VarChar(2)
  zip                       String?                          @db.VarChar(10)
  chapter                   String?                          @db.VarChar(50)
  uni_color                 String?                          @db.VarChar(50)
  uni_reverse               String?                          @default("n") @db.VarChar(1)
  flag                      String?                          @db.MediumText
  deleted                   Boolean                          @default(false)
  date_created              Int
  combined_tournament_id    Int?
  locked_row                YesNo                            @default(n)
  locked_columns            String?                          @db.VarChar(511)
  athletes                  athlete_master[]
  athlete_users             athlete_user[]
  club_staff_roster         club_staff_roster[]
  team_master               team_master[]

  @@index([organization_code], map: "Index_1")
  @@index([date_created], map: "Index_2")
  @@index([age], map: "age")
  @@index([club_roster_id], map: "club_roster_id")
  @@index([club_roster_id, deleted], map: "club_roster_id+deleted")
  @@index([division], map: "division")
  @@index([team_name], map: "name")
  @@index([roster_source_provider, roster_source_team_row_id, team_master_id], map: "r_s_provider_team_id_and_master_id")
  @@index([roster_source_team_row_id], map: "roster_source_team_row_id")
  @@index([roster_source_club_row_id], map: "roster_sources_club_row_id")
  @@index([team_master_id], map: "roster_teams_master_id")
  @@index([state], map: "state")
  @@index([team_master_id, tournament_id], map: "team_master_and_tournament")
  @@index([tournament_id], map: "tournament_id")
}

model athlete_master {
  athlete_master_id             Int                                  @id @default(autoincrement())
  athlete_user_id               Int?
  old_roster_athletes_master_id Int?                                 @default(0)
  sports_id                     Int                                  @default(2) @db.SmallInt
  roster_source_id              Int?
  roster_source_ath_row_id      String?                              @db.VarChar(50)
  roster_source_last_update     DateTime?                            @db.Timestamp(0)
  roster_source_team_row_id     String?                              @db.VarChar(50)
  roster_source_club_row_id     String?                              @db.VarChar(50)
  club_roster_id                Int?                                 @default(1)
  club_master_id                Int                                  @default(1)
  team_roster_id                Int?                                 @default(1)
  team_master_id                Int                                  @default(1)
  first                         String?                              @db.VarChar(40)
  last                          String?                              @db.VarChar(40)
  haddress1                     String?                              @db.VarChar(100)
  haddress2                     String?                              @db.VarChar(50)
  hcity                         String?                              @db.VarChar(50)
  hstate                        String?                              @db.VarChar(2)
  hzip                          String?                              @db.VarChar(10)
  phoneh                        String?                              @db.VarChar(20)
  phonec                        String?                              @db.VarChar(20)
  email                         String?                              @db.VarChar(75)
  height                        Int?                                 @default(0) @db.TinyInt
  gender                        Gender                               @default(empty)
  high_acad_standard            athlete_master_high_acad_standard    @default(EMPTY_ENUM_VALUE)
  high_school                   String?                              @db.VarChar(100)
  high_school_address           String?                              @db.VarChar(150)
  scholarship_status            athlete_master_scholarship_status    @default(EMPTY_ENUM_VALUE)
  status_verified               Int?
  gradyear                      Int?                                 @db.SmallInt
  birthdate                     DateTime?                            @db.Date
  college_name                  String?                              @db.VarChar(255)
  position1                     Int?                                 @default(0)
  position2                     Int?                                 @default(0)
  position3                     Int?                                 @default(0)
  position4                     Int?                                 @default(0)
  uniform1                      Int?
  uniform2                      Int?
  soundex_first                 String?                              @db.VarChar(25)
  soundex_last                  String?                              @db.VarChar(25)
  metaphone_last                String?                              @db.VarChar(16)
  flag                          String?                              @db.VarChar(15)
  date_modified                 DateTime                             @default(now()) @updatedAt @db.Timestamp(0)
  date_created                  Int
  date_verified                 DateTime?                            @db.Timestamp(0)
  date_admin_modified           DateTime?                            @db.Timestamp(0)
  has_changes                   YesNo                                @default(n)
  usav_id                       String?                              @db.VarChar(30)
  plays_sand_vb                 YesNo?                               @default(n)
  token                         String?                              @unique(map: "token") @db.VarChar(8)
  anonymity                     YesNo?                               @default(n)
  ncsa_client_id                Int?
  usa_ntdp                      YesNo?                               @default(n)
  cp_id                         Int?
  additional_fields             String?                              @db.MediumText
  parent1_relationship          athlete_master_parent1_relationship?
  parent1_name                  String?                              @db.VarChar(40)
  parent1_email                 String?                              @db.VarChar(75)
  parent1_phonec                String?                              @db.VarChar(20)
  parent2_relationship          athlete_master_parent2_relationship?
  parent2_name                  String?                              @db.VarChar(40)
  parent2_email                 String?                              @db.VarChar(75)
  parent2_phonec                String?                              @db.VarChar(20)
  is_lead                       YesNo?
  gpa                           Decimal?                             @db.Decimal(4, 1)
  nickname                      String?                              @db.VarChar(40)
  sat_total                     Int?
  act                           Int?                                 @db.TinyInt
  admin_email_sent              admin_email_sent[]
  athlete_email                 athlete_email[]
  club_roster                   club_roster?                         @relation(fields: [club_roster_id], references: [club_roster_id], onUpdate: NoAction, map: "fk_athlete_master_club_roster_id")
  team_roster                   team_roster?                         @relation(fields: [team_roster_id], references: [team_roster_id], onUpdate: NoAction, map: "fk_athlete_master_team_roster_id")
  users                         users?                               @relation(fields: [athlete_user_id], references: [users_id], onUpdate: NoAction, map: "fk_athlete_master_user_id")
  athlete_user                  athlete_user[]
  college_athlete_evaluated     college_athlete_evaluated[]
  ta_access                     ta_access[]
  ta_access_invitation          ta_access_invitation[]

  @@index([cp_id], map: "Index_1")
  @@index([phonec], map: "Index_2")
  @@index([phoneh], map: "Index_3")
  @@index([haddress1], map: "address")
  @@index([athlete_user_id], map: "athlete_user_id")
  @@index([birthdate], map: "birthdate")
  @@index([hcity], map: "city")
  @@index([club_master_id], map: "club_master_id")
  @@index([club_master_id], map: "cmid")
  @@index([club_roster_id], map: "crid")
  @@index([date_modified], map: "date_modified")
  @@index([email], map: "email")
  @@index([first], map: "first")
  @@index([first, last], map: "first_last")
  @@index([gradyear], map: "gradyear")
  @@index([height], map: "height")
  @@index([last], map: "last")
  @@index([ncsa_client_id], map: "ncsa_client_id")
  @@index([position1], map: "position")
  @@index([position2], map: "position2")
  @@index([roster_source_ath_row_id], map: "roster_sources_ath_row_id")
  @@index([roster_source_id], map: "roster_sources_id")
  @@index([sports_id], map: "sports_id")
  @@index([hstate], map: "state")
  @@index([team_master_id], map: "tmid")
  @@index([team_roster_id], map: "trid")
  @@index([uniform1], map: "uniform")
  @@index([usav_id], map: "usav_id")
}

model athlete_user {
  athlete_user_id               Int                                    @id @default(autoincrement())
  athlete_master_id             Int?
  old_roster_athletes_master_id Int?                                   @default(0)
  sports_id                     Int                                    @default(2) @db.SmallInt
  users_id                      Int?                                   @default(0)
  membership_expires            DateTime?                              @db.Timestamp(0)
  anet_trans_id                 String?                                @db.VarChar(50)
  club_roster_id                Int?                                   @default(1)
  club_master_id                Int?                                   @default(1)
  team_roster_id                Int?                                   @default(1)
  team_master_id                Int?                                   @default(1)
  team_age_group                String?                                @db.VarChar(5)
  team_rank                     String?                                @db.VarChar(5)
  team_name                     String?                                @db.VarChar(100)
  first                         String?                                @db.VarChar(40)
  nick                          String?                                @db.VarChar(40)
  last                          String?                                @db.VarChar(40)
  haddress1                     String?                                @db.VarChar(100)
  haddress2                     String?                                @db.VarChar(50)
  hcity                         String?                                @db.VarChar(50)
  hstate                        String?                                @db.VarChar(2)
  hzip                          String?                                @db.VarChar(10)
  phoneh                        String?                                @db.VarChar(20)
  phonec                        String?                                @db.VarChar(20)
  email                         String?                                @db.VarChar(75)
  height                        Int?                                   @default(0) @db.TinyInt
  gender                        Gender?                                @default(empty)
  high_acad_standard            athlete_user_high_acad_standard?       @default(EMPTY_ENUM_VALUE)
  gpa                           Decimal?                               @db.Decimal(4, 1)
  gpa_scale                     Decimal?                               @db.Decimal(4, 1)
  gpa_bak                       String?                                @db.VarChar(6)
  high_school                   String?                                @db.VarChar(100)
  high_school_address           String?                                @db.VarChar(150)
  act                           Int?                                   @db.TinyInt
  satm                          String?                                @db.VarChar(4)
  satv                          String?                                @db.VarChar(4)
  sate                          String?                                @db.VarChar(4)
  satcr                         String?                                @db.VarChar(4)
  satw                          String?                                @db.VarChar(4)
  scholarship_status            athlete_user_scholarship_status?       @default(EMPTY_ENUM_VALUE)
  status_verified               Int?
  gradyear                      Int?                                   @db.SmallInt
  birthdate                     DateTime?                              @db.Date
  college_name                  String?                                @db.VarChar(255)
  photo_links                   String?                                @db.VarChar(255)
  video_links                   String?                                @db.VarChar(255)
  blog_links                    String?                                @db.VarChar(255)
  position1                     Int?                                   @default(0)
  position2                     Int?                                   @default(0)
  position3                     Int?                                   @default(0)
  position4                     Int?                                   @default(0)
  uniform1                      Int?
  uniform2                      Int?
  soundex_first                 String?                                @db.VarChar(25)
  soundex_last                  String?                                @db.VarChar(25)
  flag                          String?                                @db.VarChar(15)
  date_modified                 DateTime                               @default(now()) @updatedAt @db.Timestamp(0)
  date_created                  Int?
  date_verified                 DateTime?                              @db.Timestamp(0)
  date_admin_modified           DateTime?                              @db.Timestamp(0)
  weight                        String?                                @db.VarChar(6)
  reach                         String?                                @db.VarChar(6)
  approach                      String?                                @db.VarChar(6)
  block                         String?                                @db.VarChar(6)
  handed                        Handedness?
  throws                        Handedness?
  bats                          String?                                @db.VarChar(6)
  club_age                      Int?                                   @db.UnsignedTinyInt
  club_rank                     Int?                                   @db.UnsignedTinyInt
  parent1_relationship          athlete_user_parent1_relationship?
  parent1_name                  String?                                @db.VarChar(40)
  parent1_email                 String?                                @db.VarChar(75)
  parent1_phonec                String?                                @db.VarChar(20)
  parent2_relationship          athlete_user_parent2_relationship?
  parent2_name                  String?                                @db.VarChar(40)
  parent2_email                 String?                                @db.VarChar(75)
  parent2_phonec                String?                                @db.VarChar(20)
  level                         String?                                @db.VarChar(25)
  notes                         String?                                @db.VarChar(512)
  high_school_city              String?                                @db.VarChar(50)
  high_school_state             String?                                @db.VarChar(2)
  high_school_zip               String?                                @db.VarChar(10)
  sat_total                     Int?
  club_coach_name               String?                                @db.VarChar(40)
  club_coach_email              String?                                @db.VarChar(75)
  club_coach_phone              String?                                @db.VarChar(20)
  club_director_name            String?                                @db.VarChar(40)
  club_director_email           String?                                @db.VarChar(75)
  club_director_phone           String?                                @db.VarChar(20)
  approved                      YesNo                                  @default(y)
  link_facebook                 String?                                @db.VarChar(550)
  link_instagram                String?                                @db.VarChar(550)
  link_twitter                  String?                                @db.VarChar(550)
  link_snapchat                 String?                                @db.VarChar(550)
  link_recruiting_service       String?                                @db.VarChar(550)
  link_maxpreps                 String?                                @db.VarChar(550)
  coordinator_name              String?                                @db.VarChar(75)
  coordinator_email             String?                                @db.VarChar(75)
  allow_camp_emails             YesNo                                  @default(y)
  audition_video_5              String?                                @db.VarChar(255)
  audition_video_30             String?                                @db.VarChar(255)
  psat                          Int?
  mvp_subscription_plan         String?                                @db.VarChar(20)
  mvp_subscription_status       String?                                @db.VarChar(15)
  mvp_subscription_expire       DateTime?                              @db.Timestamp(0)
  free_membership               YesNo?                                 @default(n)
  considered_usa_ntdp           YesNo?
  considered_usa_ntdp_date      DateTime?                              @db.Timestamp(0)
  sand_considered_usa_ntdp      athlete_user_sand_considered_usa_ntdp?
  sand_fields                   String?                                @db.LongText
  athlete                       athlete_master?                        @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_athlete_user_athlete_master_id")
  club                          club_master?                           @relation(fields: [club_master_id], references: [club_master_id], onUpdate: NoAction, map: "fk_athlete_user_club_master_id")
  club_roster                   club_roster?                           @relation(fields: [club_roster_id], references: [club_roster_id], onUpdate: NoAction, map: "fk_athlete_user_club_roster_id")
  team                          team_master?                           @relation(fields: [team_master_id], references: [team_master_id], onUpdate: NoAction, map: "fk_athlete_user_team_master_id")
  team_roster                   team_roster?                           @relation(fields: [team_roster_id], references: [team_roster_id], onUpdate: NoAction, map: "fk_athlete_user_team_roster_id")
  user                          users?                                 @relation(fields: [users_id], references: [users_id], onUpdate: NoAction, map: "fk_athlete_user_users_id")

  @@index([free_membership], map: "Index_1")
  @@index([club_master_id], map: "club_master_id")
  @@index([club_coach_email], map: "coach_email")
  @@index([coordinator_email], map: "coor_email")
  @@index([club_director_email], map: "director_email")
  @@index([first], map: "first")
  @@index([gpa], map: "gpa")
  @@index([gradyear], map: "gradyear")
  @@index([haddress1, gradyear], map: "haddress1")
  @@index([height], map: "height")
  @@index([hstate], map: "hstate")
  @@index([hzip], map: "hzip")
  @@index([last], map: "last")
  @@index([parent1_email], map: "p1_email")
  @@index([parent2_email], map: "p2_email")
  @@index([position1], map: "position1")
  @@index([athlete_master_id], map: "roster_athletes_master_id")
  @@index([club_roster_id], map: "roster_clubs_id")
  @@index([team_roster_id], map: "roster_teams_id")
  @@index([soundex_first], map: "soundex_first")
  @@index([sports_id], map: "sports_id")
  @@index([team_master_id], map: "team_master_id")
  @@index([users_id], map: "users_id")
  @@index([haddress1(length: 8), gradyear], map: "haddress1_2")
  @@index([haddress1(length: 8)], map: "haddress1_3")
  @@index([soundex_last, last(length: 2)], map: "soundex_last")
  @@index([soundex_last, last(length: 2), first(length: 2)], map: "soundex_last_2")
}

model athlete_team {
  athlete_team_id   Int      @id @default(autoincrement())
  created           DateTime @default(now()) @db.Timestamp(0)
  modified          DateTime @default(now()) @updatedAt @db.Timestamp(0)
  athlete_master_id Int
  team_master_id    Int
  club_master_id    Int
  team_roster_id    Int?
  tournament_id     Int?
}

model athlete_roster {
  athlete_roster_id                 Int                                 @id @default(autoincrement())
  athlete_master_id                 Int?
  old_roster_athletes_master_id     Int?
  old_roster_athletes_id            Int?
  sports_id                         Int                                 @default(2) @db.SmallInt
  roster_source_id                  Int?
  roster_source_provider            String?                             @db.VarChar(4)
  roster_source_ath_row_id          String?                             @db.VarChar(50)
  roster_source_last_update         DateTime?                           @db.Timestamp(0)
  roster_source_status              athlete_roster_roster_source_status @default(EMPTY_ENUM_VALUE)
  roster_source_team_row_id         String?                             @db.VarChar(50)
  roster_source_club_row_id         String?                             @db.VarChar(50)
  auto_match_master_id              Int?
  tournament_id                     Int?
  club_roster_id                    Int?                                @default(1)
  club_master_id                    Int?                                @default(1)
  team_roster_id                    Int?                                @default(1)
  team_master_id                    Int?                                @default(1)
  first                             String?                             @db.VarChar(40)
  last                              String?                             @db.VarChar(40)
  haddress1                         String?                             @db.VarChar(100)
  haddress2                         String?                             @db.VarChar(50)
  hcity                             String?                             @db.VarChar(50)
  hstate                            String?                             @db.VarChar(2)
  hzip                              String?                             @db.VarChar(10)
  hzip_flag                         String?                             @db.VarChar(2)
  phoneh                            String?                             @db.VarChar(20)
  phonec                            String?                             @db.VarChar(20)
  email                             String?                             @db.VarChar(75)
  height                            Int?                                @db.TinyInt
  gender                            athlete_roster_gender?
  high_acad_standard                athlete_roster_high_acad_standard?  @default(EMPTY_ENUM_VALUE)
  high_school                       String?                             @db.VarChar(100)
  high_school_address               String?                             @db.VarChar(150)
  scholarship_status                athlete_roster_scholarship_status   @default(EMPTY_ENUM_VALUE)
  status_verified                   Int?
  gradyear                          Int?                                @db.SmallInt
  birthdate                         DateTime?                           @db.Date
  college_name                      String?                             @db.VarChar(255)
  position1                         Int?                                @default(0)
  position2                         Int?                                @default(0)
  position3                         Int?                                @default(0)
  position4                         Int?                                @default(0)
  uniform1                          String?                             @db.VarChar(10)
  uniform2                          Int?
  gpa                               Decimal?                            @db.Decimal(4, 1)
  act                               Int?                                @db.TinyInt
  satm                              String?                             @db.VarChar(4)
  satv                              String?                             @db.VarChar(4)
  sate                              String?                             @db.VarChar(4)
  satcr                             String?                             @db.VarChar(4)
  satw                              String?                             @db.VarChar(4)
  handed                            Handedness?
  throws                            Handedness?
  bats                              String?                             @db.VarChar(6)
  soundex_first                     String?                             @db.VarChar(25)
  soundex_last                      String?                             @db.VarChar(25)
  metaphone_last                    String?                             @db.VarChar(16)
  flag                              String?                             @db.MediumText
  date_modified                     DateTime                            @default(now()) @updatedAt @db.Timestamp(0)
  date_created                      Int
  date_verified                     DateTime?                           @db.Timestamp(0)
  date_admin_modified               DateTime?                           @db.Timestamp(0)
  usav_id                           String?                             @db.VarChar(30)
  combined_tournament_id            Int?
  notified                          DateTime?                           @db.DateTime(0)
  locked_row                        YesNo                               @default(n)
  locked_columns                    String?                             @db.VarChar(511)
  currently_used_in_pair_auto_match YesNo                               @default(n)
  additional_fields                 String?                             @db.MediumText
  parent1_name                      String?                             @db.VarChar(40)
  parent1_email                     String?                             @db.VarChar(75)
  parent1_phonec                    String?                             @db.VarChar(20)
  parent2_name                      String?                             @db.VarChar(40)
  parent2_email                     String?                             @db.VarChar(75)
  parent2_phonec                    String?                             @db.VarChar(20)
  is_lead                           YesNo?
  nickname                          String?                             @db.VarChar(40)
  ncsa_client_id                    Int?
  hash_email_status                 String?                             @db.VarChar(25)
  sat_total                         Int?
  lead_type                         LeadType?
  calendly_url                      String?                             @db.VarChar(500)
  calendly_opened                   YesNo?
  filled_update_roster_form         FilledUpdateRosterForm?

  @@index([athlete_master_id], map: "amid")
  @@index([club_master_id], map: "club_master_id")
  @@index([date_created], map: "date_created")
  @@index([date_modified], map: "date_modified")
  @@index([email], map: "email")
  @@index([first], map: "first")
  @@index([gradyear], map: "gradyear")
  @@index([hash_email_status], map: "hash_email_status")
  @@index([height], map: "height")
  @@index([hstate], map: "hstate")
  @@index([last], map: "last")
  @@index([position1], map: "position")
  @@index([roster_source_provider, roster_source_ath_row_id, athlete_master_id], map: "r_s_provider_row_id_and_master_id")
  @@index([club_roster_id], map: "roster_clubs_id")
  @@index([roster_source_status], map: "roster_source_status")
  @@index([roster_source_team_row_id, tournament_id], map: "roster_source_team_id_and_tournament_id")
  @@index([roster_source_ath_row_id], map: "roster_sources_ath_row_id")
  @@index([roster_source_id], map: "roster_sources_id")
  @@index([team_roster_id], map: "roster_teams_id")
  @@index([team_master_id], map: "team_master_id")
  @@index([tournament_id, roster_source_status], map: "tid_rss")
  @@index([tournament_id, roster_source_ath_row_id], map: "tournament_and_roster_id")
  @@index([tournament_id], map: "tournament_id")
  @@index([uniform1], map: "uniform")
  @@index([usav_id], map: "usav_id")
}

model club_staff_master {
  club_staff_master_id       Int                      @id @default(autoincrement())
  old_roster_staff_master_id Int?
  sports_id                  Int                      @db.SmallInt
  roles_id                   Int                      @default(1)
  club_master_id             Int                      @default(1)
  team_master_id             Int                      @default(1)
  birthdate                  DateTime?                @db.Date
  gender                     String?                  @db.VarChar(1)
  first                      String?                  @db.VarChar(40)
  nick                       String?                  @db.VarChar(40)
  last                       String?                  @db.VarChar(40)
  phoneh                     String?                  @db.VarChar(20)
  phonec                     String?                  @db.VarChar(20)
  phonew                     String?                  @db.VarChar(20)
  email                      String?                  @db.VarChar(255)
  haddress1                  String?                  @db.VarChar(100)
  haddress2                  String?                  @db.VarChar(50)
  hcity                      String?                  @db.VarChar(50)
  hstate                     String?                  @db.VarChar(2)
  hzip                       String?                  @db.VarChar(10)
  waddress1                  String?                  @db.VarChar(50)
  waddress2                  String?                  @db.VarChar(50)
  wstate                     String?                  @db.VarChar(2)
  wcity                      String?                  @db.VarChar(25)
  wzip                       String?                  @db.VarChar(10)
  date_modified              DateTime                 @default(now()) @updatedAt @db.Timestamp(0)
  master_row                 Int?
  usav_id                    String?                  @db.VarChar(30)
  date_created               Int                      @default(0)
  club_staff_master_role     club_staff_master_role[]
  rosters                    club_staff_roster[]

  @@unique([old_roster_staff_master_id, club_staff_master_id], map: "old_id_and_csm_id")
  @@index([club_master_id], map: "club_master_id")
  @@index([club_master_id, roles_id], map: "club_master_id+roles_id")
  @@index([email(length: 191)], map: "email")
  @@index([first], map: "first")
  @@index([last], map: "last")
  @@index([roles_id], map: "roles_id")
  @@index([team_master_id], map: "roster_teams_id")
  @@index([last, first, club_master_id], map: "search_by_name_index")
  @@index([sports_id], map: "sports_id")
}

model club_staff_roster {
  club_staff_roster_id              Int                                     @id @default(autoincrement())
  club_staff_master_id              Int?
  old_roster_staff_id               Int?
  sports_id                         Int                                     @db.SmallInt
  roster_source_id                  Int?
  roster_source_provider            String?                                 @db.VarChar(4)
  roster_source_staff_row_id        String?                                 @db.VarChar(50)
  roster_source_last_update         DateTime?                               @db.Timestamp(0)
  roster_source_status              club_staff_roster_roster_source_status?
  roster_source_team_row_id         String?                                 @db.VarChar(50)
  roster_source_club_row_id         String?                                 @db.VarChar(50)
  auto_match_master_id              Int?
  roles_id                          Int                                     @default(1)
  tournament_id                     Int?
  club_roster_id                    Int?
  club_master_id                    Int?
  team_roster_id                    Int?
  team_master_id                    Int?
  birthdate                         DateTime?                               @db.Date
  gender                            String?                                 @db.VarChar(1)
  first                             String?                                 @db.VarChar(40)
  nick                              String?                                 @db.VarChar(40)
  last                              String?                                 @db.VarChar(40)
  phoneh                            String?                                 @db.VarChar(20)
  phonec                            String?                                 @db.VarChar(20)
  phonew                            String?                                 @db.VarChar(20)
  email                             String?                                 @db.VarChar(255)
  haddress1                         String?                                 @db.VarChar(100)
  haddress2                         String?                                 @db.VarChar(50)
  hcity                             String?                                 @db.VarChar(50)
  hstate                            String?                                 @db.VarChar(2)
  hzip                              String?                                 @db.VarChar(10)
  waddress1                         String?                                 @db.VarChar(50)
  waddress2                         String?                                 @db.VarChar(50)
  wstate                            String?                                 @db.VarChar(2)
  wcity                             String?                                 @db.VarChar(25)
  wzip                              String?                                 @db.VarChar(10)
  date_modified                     DateTime                                @default(now()) @updatedAt @db.Timestamp(0)
  combined_tournament_id            Int?
  usav_id                           String?                                 @db.VarChar(30)
  locked_row                        YesNo                                   @default(n)
  locked_columns                    String?                                 @db.VarChar(511)
  date_created                      Int                                     @default(0)
  currently_used_in_pair_auto_match YesNo?                                  @default(n)
  club_master                       club_master?                            @relation(fields: [club_master_id], references: [club_master_id], onUpdate: NoAction, map: "fk_club_staff_roster_club_master_id")
  club_roster                       club_roster?                            @relation(fields: [club_roster_id], references: [club_roster_id], onUpdate: NoAction, map: "fk_club_staff_roster_club_roster_id")
  staff                             club_staff_master?                      @relation(fields: [club_staff_master_id], references: [club_staff_master_id], onUpdate: NoAction, map: "fk_club_staff_roster_club_staff_master_id")
  team_master                       team_master?                            @relation(fields: [team_master_id], references: [team_master_id], onUpdate: NoAction, map: "fk_club_staff_roster_team_master_id")
  team_roster                       team_roster?                            @relation(fields: [team_roster_id], references: [team_roster_id], onUpdate: NoAction, map: "fk_club_staff_roster_team_roster_id")
  tournament                        tournament?                             @relation(fields: [tournament_id], references: [tournament_id], onUpdate: NoAction, map: "fk_club_staff_roster_tournament_id")

  @@unique([old_roster_staff_id, club_staff_roster_id], map: "old_id_and_scr_id")
  @@index([email(length: 191)], map: "email")
  @@index([club_master_id], map: "fk_club_staff_roster_club_master_id")
  @@index([team_master_id], map: "fk_club_staff_roster_team_master_id")
  @@index([roster_source_provider, roster_source_staff_row_id, club_staff_master_id], map: "r_s_provider_staff_id_and_master_id")
  @@index([roles_id], map: "roles_id")
  @@index([club_roster_id], map: "roster_clubs_id")
  @@index([roster_source_club_row_id], map: "roster_sources_club_row_id")
  @@index([roster_source_id], map: "roster_sources_id")
  @@index([roster_source_team_row_id], map: "roster_sources_team_row_id")
  @@index([club_staff_master_id], map: "roster_staff_master_id")
  @@index([team_roster_id], map: "roster_teams_id")
  @@index([sports_id], map: "sports_id")
  @@index([tournament_id], map: "tournaments_id")
}

model tournament {
  tournament_id                                                  Int                          @id
  sports_id                                                      Int                          @db.SmallInt
  name                                                           String?                      @db.VarChar(100)
  long_name                                                      String?                      @db.MediumText
  provider                                                       String?                      @default("") @db.VarChar(4)
  provider_tournament_id                                         String?                      @default("") @db.VarChar(50)
  date_modified                                                  DateTime                     @default(now()) @updatedAt @db.Timestamp(0)
  datestart                                                      DateTime                     @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  dateend                                                        DateTime                     @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  display                                                        Int?                         @default(0) @db.TinyInt
  downloadable                                                   YesNo?                       @default(n)
  additional_info                                                String?                      @db.MediumText
  showcase_event                                                 tournament_showcase_event    @default(n)
  free                                                           tournament_free              @default(n)
  use_after_date                                                 tournament_use_after_date    @default(n)
  min_age                                                        Int?                         @default(15)
  max_age                                                        Int?                         @default(18)
  date_pig_available                                             DateTime?                    @db.Timestamp(0)
  has_alpha_courts                                               tournament_has_alpha_courts? @default(n)
  last_import                                                    DateTime?                    @db.Timestamp(0)
  city                                                           String                       @default("") @db.VarChar(50)
  state                                                          String                       @default("") @db.VarChar(2)
  facility                                                       String                       @default("") @db.VarChar(4096)
  teams_hidden                                                   YesNo                        @default(n)
  clubs_hidden                                                   YesNo                        @default(n)
  has_admission_barcode                                          YesNo                        @default(n)
  push_club_roster_to_master                                     YesNo                        @default(y)
  push_team_roster_to_master                                     YesNo                        @default(y)
  push_athlete_roster_to_master                                  YesNo                        @default(y)
  plays_sand_vb                                                  YesNo?                       @default(n)
  json                                                           String?                      @default("{\"split_schedule_by_days\": false, \"split_schedule_by_divisions\": false}") @db.VarChar(4096)
  import_14_open                                                 YesNo?                       @default(n)
  import_divisions                                               String?                      @default("") @db.VarChar(4096)
  import_divisions_mode                                          ImportDivisionsMode?         @default(and)
  aes_event_url                                                  String?                      @db.VarChar(255)
  schedule_provider_type                                         String?                      @db.VarChar(50)
  schedule_provider_tournament_id                                String?                      @db.VarChar(255)
  sw_event_id                                                    Int?
  tournament_director_id                                         Int?
  virtual                                                        YesNo?                       @default(n)
  requires_access_code                                           String?                      @default("") @db.VarChar(50)
  ncsa_event_id                                                  Int?
  auto_sync_ncsa                                                 YesNo                        @default(n)
  sync_athletes_with_ncsa                                        YesNoEmpty?                  @default(empty)
  is_usav_event                                                  YesNo                        @default(n)
  download_all_teams                                             YesNo                        @default(n)
  size                                                           Size?                        @default(ANY)
  post_event_purchase                                            YesNo?
  scheduler_enabled                                              Boolean?                     @default(false)
  scheduler_config                                               Json?
  scheduler_hash_team                                            String?                      @db.VarChar(255)
  scheduler_hash_event                                           String?                      @db.VarChar(255)
  scheduler_hash_schedule                                        String?                      @db.VarChar(255)
  event_owner_sport_id                                           Int?
  information                                                    String?                      @db.VarChar(2000)
  event_billing_id                                               Int?
  roster_provider_id                                             Int?
  roster_provider_note                                           String?                      @db.VarChar(200)
  schedule_provider_id                                           Int?
  schedule_provider_note                                         String?                      @db.VarChar(200)
  club_staff_roster                                              club_staff_roster[]
  scheduler_court_map                                            scheduler_court_map[]
  scheduler_court_summary                                        scheduler_court_summary[]
  scheduler_team_map                                             scheduler_team_map[]
  scheduler_unknown_team                                         scheduler_unknown_team[]
  event_owner                                                    event_owner?                 @relation(fields: [event_billing_id], references: [event_owner_id], onUpdate: NoAction, map: "event_billing_id")
  event_owner_sport                                              event_owner_sport?           @relation(fields: [event_owner_sport_id], references: [event_owner_sport_id], onUpdate: NoAction, map: "event_owner_sport_id")
  event_provider_tournament_roster_provider_idToevent_provider   event_provider?              @relation("tournament_roster_provider_idToevent_provider", fields: [roster_provider_id], references: [event_provider_id], onUpdate: NoAction, map: "roster_provider_id")
  event_provider_tournament_schedule_provider_idToevent_provider event_provider?              @relation("tournament_schedule_provider_idToevent_provider", fields: [schedule_provider_id], references: [event_provider_id], onUpdate: NoAction, map: "schedule_provider_id")

  @@index([provider_tournament_id], map: "Index_1")
  @@index([datestart, dateend], map: "datestart_dateend")
  @@index([dateend], map: "de")
  @@index([display], map: "display")
  @@index([datestart], map: "ds")
  @@index([event_billing_id], map: "event_billing_id_idx")
  @@index([event_owner_sport_id], map: "event_owner_sport_id_idx")
  @@index([free], map: "free")
  @@index([free, display], map: "free_display")
  @@index([roster_provider_id], map: "roster_provider_id_idx")
  @@index([schedule_provider_id], map: "schedule_provider_id_idx")
  @@index([sports_id, display, tournament_id], map: "sport+display+tournament_id")
  @@index([sports_id], map: "sports_id_idxfk")
  @@index([sw_event_id], map: "sw_event_id")
  @@index([tournament_director_id], map: "tdid")
}

model tournament_attending {
  tournament_attending_id Int        @id @default(autoincrement())
  college_program_id      Int
  tournament_id           Int
  coaches                 Coaches?
  date_modified           DateTime   @default(now()) @updatedAt @db.Timestamp(0)
  attending               YesNoEmpty @default(empty)

  @@index([college_program_id, tournament_id], map: "cpid_tid")
  @@index([tournament_id], map: "ta_tid")
}

model users {
  users_id                  Int                @id @default(autoincrement())
  date_modified             DateTime           @default(now()) @updatedAt @db.Timestamp(0)
  sports_id                 Int                @db.SmallInt
  roles_id                  Int
  type                      UserType?
  username                  String             @unique(map: "username") @db.VarChar(75)
  password                  String             @db.VarChar(32)
  first                     String             @db.VarChar(40)
  last                      String             @db.VarChar(40)
  first_login               DateTime           @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  last_login                DateTime           @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  prev_last_login           DateTime?          @db.Timestamp(0)
  uar_last_login            DateTime?          @db.Timestamp(0)
  cp3_last_login            DateTime?          @db.Timestamp(0)
  date_created              DateTime           @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  email                     String             @db.VarChar(75)
  email_alternate           String?            @db.VarChar(75)
  personal                  String?            @db.MediumText
  portrait                  String?            @db.VarChar(255)
  ip                        String?            @default("") @db.VarChar(40)
  token                     String?            @db.VarChar(36)
  token_date_created        DateTime?          @db.Timestamp(0)
  avail_teams               Int?               @default(0)
  avail_individuals         Int?               @default(0)
  customer_id               String?            @db.VarChar(30)
  card_info                 String?            @db.VarChar(30)
  stripe_data               String?            @default("") @db.VarChar(512)
  stripe_card_id            String?            @db.VarChar(30)
  stripe_card_exp_date      DateTime?          @db.Date
  last_date_purchased       DateTime?          @db.Timestamp(0)
  pwd_hash                  String             @db.VarChar(255)
  pwd_salt                  String             @db.VarChar(75)
  allow_multiple_uar_logins Boolean            @default(false)
  keycloak_user_id          String?            @db.VarChar(36)
  old_username              String?            @db.VarChar(75)
  admin_email_sent          admin_email_sent[]
  athlete_master            athlete_master[]
  athlete_users             athlete_user[]
  ta_access                 ta_access[]

  @@index([email], map: "email")
  @@index([email_alternate], map: "email_alternate")
  @@index([keycloak_user_id], map: "kc_user_id")
  @@index([password], map: "password")
  @@index([pwd_hash(length: 191)], map: "pwd_hash")
  @@index([pwd_salt], map: "pwd_salt")
  @@index([roles_id], map: "roles_id")
  @@index([sports_id], map: "sports_id")
  @@index([token], map: "token")
}

model college {
  college_id                       Int      @id @default(autoincrement())
  old_colleges_id                  Int?     @default(0)
  date_modified                    DateTime @default(now()) @updatedAt @db.Timestamp(0)
  ope_id                           Int?     @default(0)
  ipeds_id                         Int?
  name                             String?  @db.VarChar(100)
  sanctioning                      String?  @db.VarChar(50)
  college_sanctioning_body_id      Int?     @default(1)
  college_sanctioning_body_id_used Int?     @default(1) @db.TinyInt
  college_conference_id            Int?     @default(1)
  address                          String?  @db.VarChar(50)
  city                             String?  @db.VarChar(50)
  state                            String?  @db.VarChar(2)
  zip                              String?  @db.VarChar(10)
  latitude                         Decimal? @db.Decimal(9, 6)
  longitude                        Decimal? @db.Decimal(9, 6)
  general_telephone                String?  @db.VarChar(15)
  financial_aid_telephone          String?  @db.VarChar(15)
  administrative_telephone         String?  @db.VarChar(15)
  url                              String?  @db.VarChar(300)
  url_athletic                     String?  @db.VarChar(100)
  undergrad_men                    Int?     @default(0)
  undergrad_women                  Int?     @default(0)
  total_enrollment                 Int?     @default(0)
  school_long_name                 String?  @db.VarChar(255)
  has_sports                       String?  @db.VarChar(1)
  hidden                           YesNo    @default(n)
  logo                             String?  @db.VarChar(256)
  index_name                       String?  @db.VarChar(100)
  index_zip                        String?  @db.VarChar(5)
  index_general_telephone          String?  @db.VarChar(15)
  index_url                        String?  @db.VarChar(50)

  @@unique([old_colleges_id, college_id], map: "old_db_id_and_college_id")
  @@index([hidden, college_sanctioning_body_id, college_conference_id], map: "hidden_sanction_and_conference")
  @@index([index_name], map: "index_name")
  @@index([index_general_telephone], map: "index_phone")
  @@index([index_url], map: "index_url")
  @@index([index_zip], map: "index_zip")
  @@index([ipeds_id], map: "ipeds_id")
  @@index([city], map: "univ_city")
  @@index([general_telephone], map: "univ_general_telephone")
  @@index([latitude], map: "univ_latitude")
  @@index([longitude], map: "univ_longitude")
  @@index([name], map: "univ_school")
  @@index([state], map: "univ_state")
  @@index([total_enrollment], map: "univ_total_enrollment")
  @@index([ope_id], map: "univ_unitid")
  @@index([ope_id], map: "univ_unitid_2")
  @@index([zip], map: "univ_zip")
}

model athlete_master_change {
  athlete_master_change_id Int          @id @default(autoincrement())
  athlete_master_id        Int
  date_created             DateTime     @default(now()) @db.Timestamp(0)
  name                     String?      @db.VarChar(100)
  value                    String?      @db.VarChar(255)
  old_value                String?      @db.VarChar(255)
  users_id                 Int?
  author_type              AuthorType
  author_entity_id         Int?
  status                   ChangeStatus @default(new)
  date_approved            DateTime?    @db.Timestamp(0)
  admin_users_id           Int?
  notes                    String?      @db.VarChar(512)
  sent                     YesNo        @default(n)

  @@index([athlete_master_id], map: "amid")
  @@index([athlete_master_id, status, name], map: "athlete_approved_name")
  @@index([author_entity_id], map: "author_entity_id")
  @@index([author_type], map: "author_type")
  @@index([author_type, author_entity_id], map: "author_type+author_entity_id")
  @@index([date_created], map: "date_created")
  @@index([date_created, status], map: "date_created+status")
  @@index([name], map: "name")
  @@index([status], map: "status")
  @@index([users_id], map: "users_id")
}

model athlete_club_team_history {
  athlete_club_team_history_id Int      @id @default(autoincrement())
  date_modified                DateTime @default(now()) @db.Timestamp(0)
  athlete_master_id            Int
  club_master_id               Int?
  club_name                    String?  @db.VarChar(150)
  team_master_id               Int?
  team_name                    String?  @db.VarChar(100)
  age                          Int?     @db.TinyInt
  rank                         String?  @db.VarChar(1)

  @@index([date_modified], map: "acth_date_modified")
  @@index([club_master_id], map: "club_master_id")
  @@index([athlete_master_id], map: "key_athlete_master_id")
  @@index([team_master_id], map: "team_master_id")
}

model action_clip_highlight_upload {
  action_clip_highlight_upload_id Int       @id @default(autoincrement())
  date_created                    DateTime? @default(now()) @db.Timestamp(0)
  date_modified                   DateTime  @default(now()) @db.Timestamp(0)
  users_id                        Int
  title                           String?   @db.VarChar(255)
  file_name                       String?   @db.VarChar(130)
  file_size                       Int?
  file_uploaded                   DateTime? @db.Timestamp(0)
  upload_signed_url               String?   @db.VarChar(500)

  @@index([date_created], map: "achu_date_created")
  @@index([file_name], map: "achu_file_name")
  @@index([file_uploaded], map: "achu_file_uploaded")
  @@index([users_id], map: "achu_users_id")
}

model admin_email_sent {
  admin_email_sent_id  Int                             @id @default(autoincrement())
  email_template_id    Int
  users_id             Int
  recipient_type       admin_email_sent_recipient_type @default(college)
  athlete_master_id    Int?
  college_program_id   Int?
  club_staff_master_id Int?
  recipient_users_id   Int?
  email                String?                         @db.VarChar(75)
  date_added           DateTime                        @default(now()) @db.Timestamp(0)
  seen                 admin_email_sent_seen           @default(n)
  date_seen            DateTime?                       @db.Timestamp(0)
  sent                 admin_email_sent_sent           @default(n)
  date_sent            DateTime?                       @db.Timestamp(0)
  options              String?                         @db.VarChar(255)
  date_processed       DateTime?                       @db.Timestamp(0)
  athlete_master       athlete_master?                 @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_admin_email_sent_athlete_master_id")
  college_program      college_program?                @relation(fields: [college_program_id], references: [college_program_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_admin_email_sent_college_program_id")
  email_template       email_template                  @relation(fields: [email_template_id], references: [email_template_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_admin_email_sent_email_template_id")
  users                users                           @relation(fields: [users_id], references: [users_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_admin_email_sent_users_id")

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([club_staff_master_id], map: "club_staff_master_id")
  @@index([college_program_id], map: "college_program")
  @@index([date_added], map: "date_added")
  @@index([date_processed], map: "date_processed")
  @@index([email], map: "email")
  @@index([email_template_id, recipient_users_id], map: "email_template_id+recipient_users_id")
  @@index([email_template_id], map: "etid")
  @@index([sent], map: "sent")
  @@index([sent, date_processed], map: "sent_date_processed")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model admin_help {
  admin_help_id Int                   @id @default(autoincrement()) @db.UnsignedInt
  order         Int?                  @db.TinyInt
  sports_id     Int?                  @db.SmallInt
  title         String?               @db.VarChar(255)
  menu_title    String?               @db.VarChar(50)
  alias         String?               @db.VarChar(30)
  text          String?               @db.MediumText
  url           String?               @db.VarChar(300)
  type          admin_help_type?
  visible       admin_help_visible?   @default(n)
  link_type     admin_help_link_type? @default(new)
  date_modified DateTime              @default(now()) @db.Timestamp(0)

  @@index([alias, type, visible], map: "alias+type+visible")
  @@index([sports_id], map: "sports_id")
  @@index([type], map: "type")
  @@index([url(length: 191)], map: "url")
  @@index([url(length: 191), type, visible, sports_id], map: "url_type_visible_sports_id")
  @@index([visible], map: "visible")
}

model admin_mailing_athletes {
  admin_mailing_athletes_id Int                          @id @default(autoincrement())
  date_created              DateTime?                    @default(now()) @db.Timestamp(0)
  date_modified             DateTime?                    @default(now()) @db.Timestamp(0)
  athlete_master_id         Int?
  email                     String?                      @db.VarChar(75)
  first                     String?                      @db.VarChar(40)
  last                      String?                      @db.VarChar(40)
  sent                      admin_mailing_athletes_sent?
  date_sent                 DateTime?                    @db.Timestamp(0)
  email_status              String?                      @db.VarChar(15)
  date_seen                 DateTime?                    @db.Timestamp(0)
  tournament_id             Int?
  sendgrid_template_id      String?                      @db.VarChar(36)
  email_template_id         Int?

  @@index([athlete_master_id], map: "amid")
  @@index([email], map: "email")
  @@index([email_status], map: "email_status")
  @@index([tournament_id], map: "tournament_id")
}

model admin_mailing_club_directors {
  admin_mailing_club_directors_id Int                                @id @default(autoincrement())
  date_created                    DateTime?                          @default(now()) @db.Timestamp(0)
  date_modified                   DateTime?                          @default(now()) @db.Timestamp(0)
  club_staff_master_id            Int?
  email                           String?                            @db.VarChar(75)
  first                           String?                            @db.VarChar(40)
  last                            String?                            @db.VarChar(40)
  sent                            admin_mailing_club_directors_sent?
  date_sent                       DateTime?                          @db.Timestamp(0)
  date_seen                       DateTime?                          @db.Timestamp(0)
  email_status                    String?                            @db.VarChar(15)
  tournament_id                   Int?
  sendgrid_template_id            String?                            @db.VarChar(36)
  email_template_id               Int?
}

model admin_mailing_coaches {
  admin_mailing_coaches_id Int                         @id @default(autoincrement())
  date_created             DateTime?                   @default(now()) @db.Timestamp(0)
  date_modified            DateTime?                   @default(now()) @db.Timestamp(0)
  users_id                 Int?
  email                    String?                     @db.VarChar(75)
  first                    String?                     @db.VarChar(40)
  last                     String?                     @db.VarChar(40)
  sent                     admin_mailing_coaches_sent?
  date_sent                DateTime?                   @db.Timestamp(0)
  email_status             String?                     @db.VarChar(15)
  date_seen                DateTime?                   @db.Timestamp(0)
  tournament_id            Int?
  sendgrid_template_id     String?                     @db.VarChar(36)
  email_template_id        Int?
}

model admin_mailing_recipients {
  admin_mailing_recipients_id        Int                                  @id @default(autoincrement())
  date_created                       DateTime?                            @default(now()) @db.Timestamp(0)
  date_modified                      DateTime?                            @default(now()) @db.Timestamp(0)
  email                              String?                              @db.VarChar(75)
  first                              String?                              @db.VarChar(40)
  last                               String?                              @db.VarChar(40)
  sent                               admin_mailing_recipients_sent?
  date_sent                          DateTime?                            @db.Timestamp(0)
  admin_mailing_recipients_email_log admin_mailing_recipients_email_log[]
  admin_mailing_recipients_tag       admin_mailing_recipients_tag[]
}

model admin_mailing_recipients_email_log {
  admin_mailing_recipients_email_log_id Int                      @id @default(autoincrement())
  admin_mailing_recipients_id           Int
  subject                               String?
  email_status                          String?                  @db.VarChar(15)
  date_seen                             DateTime?                @db.DateTime(0)
  date_modified                         DateTime?                @default(now()) @db.Timestamp(0)
  date_created                          DateTime?                @default(now()) @db.Timestamp(0)
  admin_mailing_recipients              admin_mailing_recipients @relation(fields: [admin_mailing_recipients_id], references: [admin_mailing_recipients_id], onDelete: Cascade, map: "fk_amrel_amr_id")

  @@index([admin_mailing_recipients_id], map: "amrel_amr_id")
  @@index([date_created], map: "amrel_date_created")
  @@index([email_status], map: "amrel_email_status")
}

model admin_mailing_recipients_tag {
  admin_mailing_recipients_tag_id Int                      @id @default(autoincrement())
  admin_mailing_recipients_id     Int
  tag_name                        String?
  tag_value                       String?
  admin_mailing_recipients        admin_mailing_recipients @relation(fields: [admin_mailing_recipients_id], references: [admin_mailing_recipients_id], onDelete: Cascade, map: "fk_admin_mailing_recipients_tag_admin_mailing_recipients_id")

  @@index([admin_mailing_recipients_id], map: "amrt_amr_id")
  @@index([tag_name, tag_value], map: "amrt_tag_name_value")
}

model admin_search {
  admin_search_id Int      @id @default(autoincrement())
  users_id        Int?
  type            String?  @db.VarChar(15)
  label           String?  @db.VarChar(30)
  post            String?  @db.VarChar(1024)
  date_modified   DateTime @default(now()) @db.Timestamp(0)

  @@index([label], map: "label")
}

model admin_user {
  admin_user_id          Int                               @id @default(autoincrement())
  created                DateTime?                         @default(now()) @db.Timestamp(0)
  modified               DateTime?                         @default(now()) @db.Timestamp(0)
  first                  String?                           @db.VarChar(40)
  last                   String?                           @db.VarChar(40)
  pwd_hash               String?                           @db.VarChar(100)
  username               String?                           @unique(map: "username") @db.VarChar(75)
  users_id               Int                               @unique(map: "users_id")
  type                   String?                           @db.VarChar(15)
  sports                 String?                           @db.VarChar(100)
  unlimited_sport_search admin_user_unlimited_sport_search @default(n)
}

model athlete_attach {
  athlete_attach_id  Int     @id @default(autoincrement())
  college_program_id Int
  athlete_master_id  Int
  filename           String? @db.VarChar(130)
  local_filename     String? @db.VarChar(130)
  filesize           Int
  url                String? @db.VarChar(256)

  @@index([athlete_master_id, athlete_attach_id], map: "athlete_master_id+athlete_attach_id")
  @@index([college_program_id, athlete_master_id], map: "college_program")
}

model athlete_color {
  athlete_color_id   Int       @id @default(autoincrement())
  uuid               String?   @unique(map: "uuid") @db.VarChar(36)
  date_created       DateTime? @db.Timestamp(0)
  date_modified      DateTime  @default(now()) @db.Timestamp(0)
  college_program_id Int
  coach              Boolean?
  tournament_id      Int?
  color              String?   @db.VarChar(10)
  athlete_master_id  Int
  users_id           Int?

  @@unique([college_program_id, coach, athlete_master_id], map: "program_coach_athlete")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_duplicate {
  athlete_duplicate_id Int      @id
  athlete_master_id    Int
  date_merged          DateTime @default(now()) @db.Timestamp(0)
  comment              String?  @db.VarChar(255)

  @@unique([athlete_duplicate_id, athlete_master_id], map: "athlete_duplicate_id+amid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_email {
  athlete_email_id  Int                   @id @default(autoincrement())
  athlete_master_id Int
  email             String?               @db.VarChar(75)
  original_email    String?               @db.VarChar(75)
  primary           athlete_email_primary @default(dbgenerated("0"))
  owner             athlete_email_owner   @default(other)
  source_id         Int                   @default(0)
  date_created      DateTime              @default(now()) @db.Timestamp(0)
  athlete_master    athlete_master        @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "athlete_id")

  @@index([athlete_master_id], map: "amid")
  @@index([date_created], map: "date_created")
  @@index([email], map: "email")
  @@index([original_email], map: "original_email")
  @@index([source_id], map: "source_id")
}

model athlete_email_sent {
  athlete_email_sent_id Int                          @id @default(autoincrement())
  email_template_id     Int?
  college_program_id    Int?
  date_added            DateTime                     @default(now()) @db.Timestamp(0)
  seen                  athlete_email_sent_seen      @default(n)
  date_seen             DateTime?                    @db.Timestamp(0)
  email                 String?                      @db.VarChar(75)
  sent                  athlete_email_sent_sent      @default(n)
  date_sent             DateTime?                    @db.Timestamp(0)
  users_id              Int?
  answered              athlete_email_sent_answered? @default(n)
  hidden                athlete_email_sent_hidden?   @default(n)
  options               String?                      @db.VarChar(255)
  college_user_id       Int                          @default(0)
  body                  String?                      @db.LongText
  status                String?                      @db.VarChar(15)

  @@index([status], map: "Index_1")
  @@index([college_program_id], map: "college_program")
  @@index([college_user_id], map: "college_user_id")
  @@index([college_user_id, date_added, seen], map: "cuid_date_added_seen")
  @@index([date_added], map: "date_added")
  @@index([email_template_id], map: "email_template")
  @@index([sent], map: "sent")
  @@index([sent, email_template_id], map: "sent+email_template_id")
  @@index([users_id], map: "users_id")
}

model athlete_email_source {
  athlete_email_source_id Int     @id @default(autoincrement())
  name                    String? @db.VarChar(100)
}

model athlete_event {
  athlete_event_id   Int                      @id @default(autoincrement())
  athlete_master_id  Int
  event_name         String?                  @db.VarChar(100)
  datestart          DateTime                 @db.Date
  dateend            DateTime                 @db.Date
  is_local           Boolean                  @default(false)
  event_type         athlete_event_event_type @default(EMPTY_ENUM_VALUE)
  date_modified      DateTime                 @default(now()) @db.Timestamp(0)
  team_name          String?                  @db.VarChar(100)
  position           Int                      @default(0)
  uniform            Int                      @default(0) @db.TinyInt
  city               String?                  @db.VarChar(50)
  state              String?                  @db.VarChar(2)
  facility           String?                  @db.VarChar(4096)
  tournament_id      Int                      @default(0)
  college_program_id Int?

  @@index([athlete_master_id, college_program_id], map: "amid_cpid")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_program_id], map: "college_program_id")
  @@index([is_local], map: "is_local")
  @@index([tournament_id], map: "tournament_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_favorite {
  athlete_favorite_id Int      @id @default(autoincrement())
  date_modified       DateTime @default(now()) @db.Timestamp(0)
  athlete_user_id     Int      @default(0)
  college_id          Int      @default(0)
  notes               String?  @db.VarChar(6500)
  date_created        DateTime @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  sort_order          Int?     @db.TinyInt

  @@index([athlete_user_id, college_id], map: "athlete_user_and_college")
  @@index([athlete_user_id], map: "auid")
  @@index([college_id], map: "college_id")
  @@index([sort_order], map: "sort_order")
}

model athlete_hp {
  athlete_hp_id      Int       @id @default(autoincrement())
  athlete_master_id  Int
  college_program_id Int
  users_id           Int
  date_created       DateTime? @default(now()) @db.Timestamp(0)

  @@unique([athlete_master_id, college_program_id, users_id], map: "amid_cpid_uid")
}

model athlete_ident_result {
  athlete_ident_result_id Int     @id @default(autoincrement())
  mail_turn_id            Int
  email_id                Int?
  college_note_id         Int?
  first                   String? @db.VarChar(40)
  last                    String? @db.VarChar(40)
  email                   String? @db.VarChar(75)
  gradyear                Int?    @db.SmallInt
  state                   String? @db.VarChar(2)
  phone                   String? @db.VarChar(20)
  system_note             String? @db.VarChar(100)
  athlete_master_id_find  Int     @default(0)
  accuracy                Int?
  college_program_id      Int?
  find_by_email           Int?
  target                  Int?    @db.TinyInt

  @@index([mail_turn_id], map: "Index_1")
  @@index([college_note_id], map: "Index_2")
  @@index([college_program_id], map: "Index_3")
  @@index([athlete_master_id_find], map: "Index_4")
}

model athlete_import {
  athlete_import_id         Int                                 @id @default(autoincrement())
  import_process_id         Int
  invalid_data              athlete_import_invalid_data         @default(y)
  roster_action             athlete_import_roster_action        @default(ignore)
  athlete_roster_id         Int?
  athlete_master_id         Int?
  tournament_id             Int
  found_master_dupl         athlete_import_found_master_dupl    @default(n)
  matching_probability      Int?
  matched_by                String?                             @db.VarChar(155)
  match_variety_ids         String?                             @db.VarChar(255)
  match_variety_total       Int?
  sports_id                 Int                                 @db.SmallInt
  roster_source_club_row_id String?                             @db.VarChar(50)
  roster_source_team_row_id String?                             @db.VarChar(50)
  roster_source_ath_row_id  String?                             @db.VarChar(50)
  roster_source_status      athlete_import_roster_source_status @default(EMPTY_ENUM_VALUE)
  first                     String?                             @db.VarChar(40)
  last                      String?                             @db.VarChar(40)
  haddress1                 String?                             @db.VarChar(100)
  haddress2                 String?                             @db.VarChar(50)
  hcity                     String?                             @db.VarChar(50)
  hstate                    String?                             @db.VarChar(2)
  hzip                      String?                             @db.VarChar(10)
  phoneh                    String?                             @db.VarChar(20)
  phonec                    String?                             @db.VarChar(20)
  email                     String?                             @db.VarChar(75)
  height                    Int?                                @db.TinyInt
  gender                    athlete_import_gender?
  high_school               String?                             @db.VarChar(100)
  scholarship_status        athlete_import_scholarship_status   @default(EMPTY_ENUM_VALUE)
  gradyear                  Int?                                @db.SmallInt
  birthdate                 DateTime?                           @db.Date
  college_name              String?                             @db.VarChar(255)
  position1                 Int?                                @default(0)
  position2                 Int?                                @default(0)
  position3                 Int?                                @default(0)
  position4                 Int?                                @default(0)
  uniform1                  String?                             @db.VarChar(10)
  uniform2                  Int?
  gpa                       Decimal?                            @db.Decimal(4, 1)
  act                       Int?                                @db.TinyInt
  satm                      String?                             @db.VarChar(4)
  satv                      String?                             @db.VarChar(4)
  satw                      String?                             @db.VarChar(4)
  handed                    athlete_import_handed?
  throws                    athlete_import_throws?
  bats                      String?                             @db.VarChar(6)
  usav_id                   String?                             @db.VarChar(30)
  roster_source_last_update DateTime                            @default(now()) @db.Timestamp(0)
  preparsed_data            String?                             @db.VarChar(1024)
  lock_roster_columns       String?                             @db.VarChar(511)
  imported_invalid_values   String?                             @db.VarChar(1023)
  additional_fields         String?                             @db.MediumText
  parent1_name              String?                             @db.VarChar(40)
  parent1_email             String?                             @db.VarChar(75)
  parent1_phonec            String?                             @db.VarChar(20)
  parent2_name              String?                             @db.VarChar(40)
  parent2_email             String?                             @db.VarChar(75)
  parent2_phonec            String?                             @db.VarChar(20)
  nickname                  String?                             @db.VarChar(40)
  sat_total                 Int?
  result_ml_import          result_ml_import[]

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([athlete_roster_id], map: "athlete_roster_id")
  @@index([birthdate], map: "birthdate")
  @@index([first], map: "first")
  @@index([first, last], map: "first_last")
  @@index([found_master_dupl], map: "found_master_dupl")
  @@index([hstate], map: "hstate")
  @@index([import_process_id], map: "import_process_id")
  @@index([invalid_data], map: "invalid_data")
  @@index([last], map: "last")
  @@index([lock_roster_columns(length: 191)], map: "lock_roster_columns")
  @@index([phonec], map: "phonec")
  @@index([phoneh], map: "phoneh")
  @@index([position1], map: "position1")
  @@index([roster_action], map: "roster_action")
  @@index([roster_source_ath_row_id], map: "roster_source_ath_row_id")
  @@index([roster_source_club_row_id], map: "roster_source_club_row_id")
  @@index([roster_source_last_update], map: "roster_source_last_update")
  @@index([roster_source_team_row_id], map: "roster_source_team_row_id")
  @@index([sports_id], map: "sports_id")
  @@index([tournament_id], map: "tournament_id")
  @@index([uniform1], map: "uniform1")
  @@index([usav_id], map: "usav_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_import_ml_master_snapshot {
  athlete_import_ml_master_snapshot_id Int       @id @default(autoincrement())
  athlete_import_id                    Int
  athlete_master_id                    Int
  ci_club_master_id                    Int?
  ai_last                              String?   @db.VarChar(40)
  ai_first                             String?   @db.VarChar(40)
  ai_gradyear                          Int?
  ai_birthdate                         DateTime? @db.DateTime(0)
  ai_usav_id                           String?   @db.VarChar(30)
  ai_phonec                            String?   @db.VarChar(20)
  ai_email                             String?   @db.VarChar(75)
  ai_hzip                              String?   @db.VarChar(10)
  ai_hstate                            String?   @db.VarChar(2)
  ai_hcity                             String?   @db.VarChar(50)
  ai_haddress1                         String?   @db.VarChar(50)
  ai_soundex_first                     String?   @db.VarChar(25)
  ai_soundex_last                      String?   @db.VarChar(25)
  ci_club_code                         String?   @db.VarChar(30)
  m_club_master_id                     Int?
  m_last                               String?   @db.VarChar(40)
  m_first                              String?   @db.VarChar(40)
  m_gradyear                           Int?
  m_birthdate                          DateTime? @db.DateTime(0)
  m_usav_id                            String?   @db.VarChar(30)
  m_phonec                             String?   @db.VarChar(20)
  m_email                              String?   @db.VarChar(75)
  m_hzip                               String?   @db.VarChar(10)
  m_hstate                             String?   @db.VarChar(2)
  m_hcity                              String?   @db.VarChar(50)
  m_haddress1                          String?   @db.VarChar(50)
  m_soundex_first                      String?   @db.VarChar(25)
  m_soundex_last                       String?   @db.VarChar(25)
  m_club_code                          String?   @db.VarChar(30)
  import_ml_process_id                 Int
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_master_archive {
  athlete_master_archive_id     Int                                          @unique(map: "athlete_master_archive_id") @default(autoincrement())
  date_archived                 DateTime                                     @default(now()) @db.Timestamp(0)
  archive_type                  athlete_master_archive_archive_type
  athlete_master_id             Int                                          @default(0)
  athlete_user_id               Int?
  old_roster_athletes_master_id Int?                                         @default(0)
  sports_id                     Int                                          @default(2) @db.SmallInt
  roster_source_id              Int?
  roster_source_ath_row_id      String?                                      @db.VarChar(50)
  roster_source_last_update     DateTime?                                    @db.Timestamp(0)
  roster_source_team_row_id     String?                                      @db.VarChar(50)
  roster_source_club_row_id     String?                                      @db.VarChar(50)
  club_roster_id                Int?
  club_master_id                Int                                          @default(1)
  team_roster_id                Int?                                         @default(1)
  team_master_id                Int
  first                         String?                                      @db.VarChar(40)
  last                          String?                                      @db.VarChar(40)
  haddress1                     String?                                      @db.VarChar(100)
  haddress2                     String?                                      @db.VarChar(50)
  hcity                         String?                                      @db.VarChar(50)
  hstate                        String?                                      @db.VarChar(2)
  hzip                          String?                                      @db.VarChar(10)
  phoneh                        String?                                      @db.VarChar(20)
  phonec                        String?                                      @db.VarChar(20)
  email                         String?                                      @db.VarChar(75)
  height                        Int?                                         @db.TinyInt
  gender                        athlete_master_archive_gender                @default(EMPTY_ENUM_VALUE)
  high_acad_standard            athlete_master_archive_high_acad_standard    @default(EMPTY_ENUM_VALUE)
  high_school                   String?                                      @db.VarChar(100)
  high_school_address           String?                                      @db.VarChar(150)
  scholarship_status            athlete_master_archive_scholarship_status    @default(EMPTY_ENUM_VALUE)
  status_verified               Int?
  gradyear                      Int?                                         @db.SmallInt
  birthdate                     DateTime?                                    @db.Date
  college_name                  String?                                      @db.VarChar(255)
  position1                     Int?                                         @default(0)
  position2                     Int?                                         @default(0)
  position3                     Int?                                         @default(0)
  position4                     Int?                                         @default(0)
  uniform1                      Int?
  uniform2                      Int?
  soundex_first                 String?                                      @db.VarChar(25)
  soundex_last                  String?                                      @db.VarChar(25)
  metaphone_last                String?                                      @db.VarChar(16)
  flag                          String?                                      @db.VarChar(15)
  date_modified                 DateTime?                                    @db.Timestamp(0)
  date_created                  Int
  date_verified                 DateTime?                                    @db.Timestamp(0)
  date_admin_modified           DateTime?                                    @db.Timestamp(0)
  has_changes                   athlete_master_archive_has_changes           @default(n)
  usav_id                       String?                                      @db.VarChar(30)
  plays_sand_vb                 athlete_master_archive_plays_sand_vb?        @default(n)
  token                         String?                                      @db.VarChar(8)
  anonymity                     athlete_master_archive_anonymity?            @default(n)
  ncsa_client_id                Int?
  usa_ntdp                      athlete_master_archive_usa_ntdp?             @default(n)
  cp_id                         Int?
  additional_fields             String?                                      @db.MediumText
  parent1_relationship          athlete_master_archive_parent1_relationship?
  parent1_name                  String?                                      @db.VarChar(40)
  parent1_email                 String?                                      @db.VarChar(75)
  parent1_phonec                String?                                      @db.VarChar(20)
  parent2_relationship          athlete_master_archive_parent2_relationship?
  parent2_name                  String?                                      @db.VarChar(40)
  parent2_email                 String?                                      @db.VarChar(75)
  parent2_phonec                String?                                      @db.VarChar(20)
  is_lead                       athlete_master_archive_is_lead?
  gpa                           Decimal?                                     @db.Decimal(4, 1)
  nickname                      String?                                      @db.VarChar(40)
  sat_total                     Int?
  act                           Int?                                         @db.TinyInt

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([email], map: "email")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model athlete_master_is_lead_before_athlete_or_parent_temp {
  athlete_master_id Int

  @@ignore
}

model athlete_master_update_hash {
  athlete_master_update_hash_id Int                          @id @default(autoincrement())
  athlete_master_id             Int
  tournament_id                 Int
  hash                          String?                      @unique(map: "hash") @db.VarChar(36)
  email_status                  String?                      @db.VarChar(15)
  date_modified                 DateTime?                    @default(now()) @db.Timestamp(0)
  date_created                  DateTime?                    @default(now()) @db.Timestamp(0)
  athlete_log                   String?                      @db.Text
  date_sent                     DateTime?                    @db.Timestamp(0)
  update_hash_email_tracking    update_hash_email_tracking[]

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([tournament_id], map: "tournament_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_match {
  athlete_match_id  Int     @id @default(autoincrement())
  athlete_master_id Int     @unique(map: "athlete_master_id")
  club_roster_id    Int?
  club_master_id    Int?
  first             String? @db.VarChar(40)
  first_2           String? @db.VarChar(2)
  last              String? @db.VarChar(40)
  last_3            String? @db.VarChar(3)
  haddress1_8       String? @db.VarChar(8)
  hstate            String? @db.VarChar(2)
  hzip              String? @db.VarChar(10)
  hzip_5            String? @db.VarChar(5)
  hzip_3            String? @db.VarChar(3)
  gradyear          Int?    @db.SmallInt
  soundex_first     String? @db.VarChar(25)
  soundex_last      String? @db.VarChar(25)
  metaphone_last    String? @db.VarChar(16)
  usav_id           String? @db.VarChar(30)

  @@index([club_master_id], map: "club_master_id")
  @@index([club_roster_id], map: "club_roster_id")
  @@index([first_2, last_3, hstate], map: "first_2+last_3+hstate")
  @@index([first_2, last], map: "first_2_and_last")
  @@index([gradyear], map: "gradyear")
  @@index([hzip], map: "hzip")
  @@index([last, club_master_id, first], map: "last_club_and_first")
  @@index([metaphone_last, first_2], map: "mp_last_and_first_2")
  @@index([hstate, club_roster_id, gradyear, first_2, haddress1_8, last], map: "search1_idx")
  @@index([usav_id], map: "usav_id")
}

model athlete_open_contact {
  athlete_open_contact_id Int      @id @default(autoincrement())
  athlete_master_id       Int
  college_program_id      Int
  date_created            DateTime @default(now()) @db.Timestamp(0)

  @@unique([athlete_master_id, college_program_id], map: "amid_cpid")
  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
}

model athlete_rank {
  athlete_rank_id    Int      @id @default(autoincrement())
  date_modified      DateTime @default(now()) @db.Timestamp(0)
  college_rank_id    Int
  athlete_master_id  Int
  college_program_id Int
  tournament_id      Int?     @default(0)
  users_id           Int
  value              String?  @db.VarChar(10)

  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([college_rank_id], map: "crid")
  @@index([date_modified], map: "dm")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "uid")
}

model athlete_rank_history {
  athlete_rank_history_id Int       @id @default(autoincrement())
  date_created            DateTime? @default(now()) @db.Timestamp(0)
  date_deleted            DateTime? @db.Timestamp(0)
  college_rank_id         Int
  athlete_master_id       Int
  college_program_id      Int
  tournament_id           Int?      @default(0)
  users_id                Int
  value                   String?   @db.VarChar(10)

  @@index([athlete_master_id], map: "amid")
  @@index([date_created], map: "arh_date_created")
  @@index([college_program_id], map: "cpid")
  @@index([college_rank_id], map: "crid")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "uid")
}

model athlete_request {
  athlete_request_id Int                          @id @default(autoincrement())
  users_id           Int
  college_program_id Int
  athlete_master_id  Int
  date_created       DateTime                     @default(now()) @db.Timestamp(0)
  request_type       athlete_request_request_type @default(details)
  ip                 String?                      @db.VarChar(40)
  tournament_id      Int?
  source             String?                      @db.VarChar(15)

  @@index([athlete_master_id], map: "amid")
  @@index([source], map: "areq_source")
  @@index([tournament_id], map: "areq_tid")
  @@index([college_program_id], map: "cpid")
  @@index([date_created], map: "dc")
  @@index([users_id], map: "users_id")
}

model athlete_request_anonymity {
  athlete_request_anonymity_id Int                               @id @default(autoincrement())
  sports_id                    Int                               @db.SmallInt
  athlete_master_id            Int?
  first                        String?                           @db.VarChar(45)
  last                         String?                           @db.VarChar(40)
  email                        String?                           @db.VarChar(75)
  usav_id                      String?                           @db.VarChar(30)
  comment                      String?                           @db.VarChar(500)
  date_created                 DateTime?                         @default(now()) @db.Timestamp(0)
  photo                        String?                           @db.VarChar(45)
  date_approved                DateTime?                         @db.Timestamp(0)
  status                       athlete_request_anonymity_status? @default(new)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_roster_master_snapshot {
  athlete_roster_id Int       @id
  athlete_master_id Int
  club_master_id    Int?
  first             String?   @db.VarChar(40)
  last              String?   @db.VarChar(40)
  gradyear          Int?      @db.SmallInt
  birthdate         DateTime? @db.Date
  usav_id           String?   @db.VarChar(30)
  phonec            String?   @db.VarChar(20)
  email             String?   @db.VarChar(75)
  hzip              String?   @db.VarChar(10)
  hstate            String?   @db.VarChar(2)
  hcity             String?   @db.VarChar(50)
  haddress1         String?   @db.VarChar(100)
  soundex_first     String?   @db.VarChar(25)
  soundex_last      String?   @db.VarChar(25)
  club_code         String?   @db.VarChar(30)
  m_club_master_id  Int?
  m_first           String?   @db.VarChar(40)
  m_last            String?   @db.VarChar(40)
  m_gradyear        Int?
  m_birthdate       DateTime? @db.Date
  m_usav_id         String?   @db.VarChar(30)
  m_phonec          String?   @db.VarChar(20)
  m_email           String?   @db.VarChar(75)
  m_hzip            String?   @db.VarChar(10)
  m_hstate          String?   @db.VarChar(2)
  m_hcity           String?   @db.VarChar(50)
  m_haddress1       String?   @db.VarChar(100)
  m_soundex_first   String?   @db.VarChar(25)
  m_soundex_last    String?   @db.VarChar(25)
  m_club_code       String?   @db.VarChar(30)
}

model athlete_tag {
  athlete_tag_id     Int       @id @default(autoincrement())
  date_created       DateTime? @default(now()) @db.Timestamp(0)
  date_modified      DateTime  @default(now()) @db.Timestamp(0)
  college_tag_id     Int
  athlete_master_id  Int
  college_program_id Int
  tournament_id      Int?      @default(0)
  users_id           Int
  cp_id              Int?

  @@index([cp_id], map: "Index_1")
  @@index([date_created], map: "Index_2")
  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([college_tag_id], map: "ctid")
  @@index([date_modified], map: "dm")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "uid")
}

model athlete_tag_history {
  athlete_tag_history_id Int       @id @default(autoincrement())
  date_created           DateTime  @default(now()) @db.Timestamp(0)
  date_modified          DateTime  @default(now()) @db.Timestamp(0)
  date_deleted           DateTime? @db.Timestamp(0)
  college_tag_id         Int
  athlete_master_id      Int
  college_program_id     Int
  tournament_id          Int?      @default(0)
  users_id               Int

  @@index([date_created], map: "Index_1")
  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([college_tag_id], map: "ctid")
  @@index([date_modified], map: "dm")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "uid")
}

model athlete_task {
  athlete_task_id                Int                  @id @default(autoincrement())
  date_created                   DateTime?            @default(now()) @db.Timestamp(0)
  date_modified                  DateTime?            @default(now()) @db.Timestamp(0)
  date_deleted                   DateTime?            @db.Timestamp(0)
  athlete_master_id              Int
  college_program_id             Int
  college_task_id                Int?
  college_task_column_id         Int?
  college_task_column_divider_id Int?
  assigned_to_user_id            Int?
  task                           String?              @db.VarChar(255)
  tournament_id                  Int?                 @default(0)
  due_date                       DateTime?            @db.Timestamp(0)
  date_completed                 DateTime?            @db.Timestamp(0)
  sort_order                     Int?                 @db.TinyInt
  created_users_id               Int?
  priority_id                    Int?
  college_task_status_id         Int?
  email_template_id              Int?
  done_college_task_id           Int?
  description                    String?              @db.VarChar(300)
  mailing_template_id            Int?
  uuid                           String?              @db.VarChar(36)
  done_college_tag_id            Int?
  college_task                   college_task?        @relation(fields: [college_task_id], references: [college_task_id], onUpdate: NoAction, map: "fk_athlete_task_college_task_id")
  college_task_status            college_task_status? @relation(fields: [college_task_status_id], references: [college_task_status_id], onUpdate: NoAction, map: "fk_athlete_task_college_task_status_id")
  email_template_new             email_template_new?  @relation(fields: [email_template_id], references: [email_template_new_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_athlete_task_email_template_id")
  priorities                     priorities?          @relation(fields: [priority_id], references: [priorities_id], onUpdate: NoAction, map: "fk_athlete_task_priority_id")

  @@index([athlete_master_id], map: "amid")
  @@index([college_task_status_id], map: "college_task_status_id_idx")
  @@index([college_program_id], map: "cpid")
  @@index([college_task_id], map: "ctid")
  @@index([date_completed], map: "date_completed")
  @@index([date_deleted], map: "dd")
  @@index([date_modified], map: "dm")
  @@index([due_date], map: "due_date")
  @@index([email_template_id], map: "fk_email_template_id_idx")
  @@index([priority_id], map: "priority_id_idx")
  @@index([assigned_to_user_id], map: "uid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_team_coach_change {
  athlete_team_coach_change_id Int                              @id @default(autoincrement())
  athlete_master_id            Int
  name                         String?                          @db.VarChar(100)
  value                        String?                          @db.VarChar(150)
  old_value                    String?                          @db.VarChar(255)
  status                       athlete_team_coach_change_status @default(new)
  users_id                     Int?
  tournament_id                Int?
  author_type                  String?                          @db.VarChar(25)
  sent                         athlete_team_coach_change_sent   @default(n)
  date_created                 DateTime?                        @default(now()) @db.Timestamp(0)
  date_modified                DateTime?                        @default(now()) @db.Timestamp(0)

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([author_type], map: "author_type")
  @@index([date_created], map: "date_created")
  @@index([date_created, status], map: "date_created_status")
  @@index([status], map: "status")
  @@index([tournament_id], map: "tournament_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model athlete_teammate {
  athlete_teammate_id        Int                               @id @default(autoincrement())
  created                    DateTime?                         @db.DateTime(0)
  modified                   DateTime                          @default(now()) @db.Timestamp(0)
  athlete_master_id          Int
  teammate_athlete_master_id Int
  teammate_first             String?                           @db.VarChar(40)
  teammate_last              String?                           @db.VarChar(40)
  teammate_email             String?                           @db.VarChar(75)
  teammate_cell_phone        String?                           @db.VarChar(20)
  unknown_teammate           athlete_teammate_unknown_teammate @default(n)
  email_sent                 DateTime?                         @db.Timestamp(0)
  text_sent                  DateTime?                         @db.Timestamp(0)
  has_account                athlete_teammate_has_account      @default(dbgenerated("1"))

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([teammate_athlete_master_id], map: "teammate_athlete_master_id")
}

model athlete_unsubscribe {
  athlete_unsubscribe_id Int                            @id @default(autoincrement())
  email                  String?                        @db.VarChar(75)
  comment                String?                        @db.VarChar(255)
  date_created           DateTime                       @default(now()) @db.Timestamp(0)
  ip                     String?                        @db.VarChar(40)
  promotion              athlete_unsubscribe_promotion? @default(n)
  follow                 athlete_unsubscribe_follow?    @default(n)

  @@index([email], map: "email")
}

model athlete_video {
  athlete_video_id  Int                 @id @default(autoincrement())
  users_id          Int
  athlete_master_id Int
  tournament_id     Int?                @default(0)
  sort_order        Int                 @db.TinyInt
  title             String?             @db.VarChar(255)
  date_taken        DateTime            @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  date_created      DateTime            @default(now()) @db.Timestamp(0)
  url               String?             @db.VarChar(500)
  location          String?             @db.VarChar(500)
  note              String?             @db.VarChar(512)
  api_type          String?             @db.VarChar(20)
  show              Int?                @default(0)
  seen              athlete_video_seen? @default(y)
  mvpcast_teammate  Int?
  type              athlete_video_type? @default(full)

  @@index([athlete_master_id], map: "amid")
  @@index([api_type], map: "api_type")
  @@index([show], map: "show")
  @@index([users_id], map: "users_id")
}

model athlete_want_to_college {
  athlete_want_to_college_id Int       @id @default(autoincrement())
  athlete_master_id          Int
  college_program_id         Int
  date_created               DateTime? @default(now()) @db.Timestamp(0)

  @@unique([athlete_master_id, college_program_id], map: "awtc_amid_cpid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model club_import {
  club_import_id            Int                              @id @default(autoincrement())
  import_process_id         Int
  invalid_data              club_import_invalid_data         @default(y)
  roster_action             club_import_roster_action        @default(ignore)
  club_roster_id            Int?
  club_master_id            Int?
  initial_master_id         Int?
  tournament_id             Int
  found_master_dupl         club_import_found_master_dupl    @default(n)
  matched_by                String?                          @db.VarChar(155)
  match_variety_ids         String?                          @db.VarChar(255)
  roster_source_club_row_id String?                          @db.VarChar(50)
  roster_source_status      club_import_roster_source_status @default(EMPTY_ENUM_VALUE)
  club_name                 String?                          @db.VarChar(150)
  stripped_name             String?                          @db.VarChar(100)
  region_code               String?                          @db.VarChar(5)
  club_code                 String?                          @db.VarChar(30)
  address1                  String?                          @db.VarChar(50)
  address2                  String?                          @db.VarChar(50)
  city                      String?                          @db.VarChar(50)
  state                     String?                          @db.VarChar(2)
  zip                       String?                          @db.VarChar(10)
  url                       String?                          @db.VarChar(300)
  office_phone              String?                          @db.VarChar(25)
  other_phone               String?                          @db.VarChar(25)
  fax                       String?                          @db.VarChar(25)
  email                     String?                          @db.VarChar(255)
  roster_source_last_update DateTime                         @default(now()) @db.Timestamp(0)
  preparsed_data            String?                          @db.VarChar(1023)
  lock_roster_columns       String?                          @db.VarChar(511)
  imported_invalid_values   String?                          @db.VarChar(1023)
  coordinator_email         String?                          @db.VarChar(75)

  @@index([club_master_id], map: "Index_1")
  @@index([club_roster_id], map: "club_roster_id")
  @@index([import_process_id], map: "import_process_id")
  @@index([invalid_data], map: "invalid_data")
  @@index([roster_action], map: "roster_action")
  @@index([roster_source_club_row_id], map: "roster_source_club_row_id")
  @@index([roster_source_last_update], map: "roster_source_last_update")
  @@index([tournament_id], map: "tournament_id")
}

model club_roster {
  club_roster_id            Int                               @id @default(autoincrement())
  club_master_id            Int?
  old_roster_clubs_id       Int?                              @default(0)
  roster_source_id          Int?                              @default(0)
  roster_source_provider    String?                           @db.VarChar(4)
  roster_source_club_row_id String?                           @db.VarChar(50)
  roster_source_last_update DateTime?                         @db.Timestamp(0)
  roster_source_status      club_roster_roster_source_status?
  tournament_id             Int
  club_name                 String?                           @db.VarChar(150)
  region_code               String?                           @db.VarChar(5)
  club_code                 String?                           @db.VarChar(30)
  address1                  String?                           @db.VarChar(50)
  address2                  String?                           @db.VarChar(50)
  city                      String?                           @db.VarChar(50)
  state                     String?                           @db.VarChar(2)
  zip                       String?                           @db.VarChar(10)
  url                       String?                           @db.VarChar(300)
  office_phone              String?                           @db.VarChar(25)
  other_phone               String?                           @db.VarChar(25)
  fax                       String?                           @db.VarChar(25)
  email                     String?                           @db.VarChar(255)
  date_modified             DateTime                          @default(now()) @db.Timestamp(0)
  flag                      String?                           @db.MediumText
  date_created              Int
  combined_tournament_id    Int?
  hidden                    club_roster_hidden                @default(n)
  locked_row                club_roster_locked_row            @default(n)
  locked_columns            String?                           @db.VarChar(511)
  coordinator_email         String?                           @db.VarChar(75)
  athlete_master            athlete_master[]
  athlete_user              athlete_user[]
  club_staff_roster         club_staff_roster[]

  @@index([club_master_id], map: "club_master_id")
  @@index([email(length: 191)], map: "email")
  @@index([club_name], map: "name")
  @@index([roster_source_provider, roster_source_club_row_id, club_master_id], map: "r_s_provider_club_id_and_master_id")
  @@index([roster_source_club_row_id, club_roster_id], map: "roster_source_club_row_id_and_club_roster_id")
  @@index([roster_source_club_row_id, tournament_id], map: "roster_source_club_row_id_and_tournament_id")
  @@index([roster_source_id], map: "roster_sources_id")
  @@index([state], map: "state")
  @@index([tournament_id], map: "tournaments_id")
}

model club_staff_import {
  club_staff_import_id       Int                                    @id @default(autoincrement())
  import_process_id          Int
  invalid_data               club_staff_import_invalid_data         @default(y)
  roster_action              club_staff_import_roster_action        @default(ignore)
  club_staff_roster_id       Int?
  club_staff_master_id       Int?
  sports_id                  Int                                    @db.SmallInt
  roster_source_club_row_id  String?                                @db.VarChar(50)
  roster_source_team_row_id  String?                                @db.VarChar(50)
  roster_source_staff_row_id String?                                @db.VarChar(50)
  tournament_id              Int
  found_master_dupl          club_staff_import_found_master_dupl    @default(n)
  matched_by                 String?                                @db.VarChar(155)
  roster_source_status       club_staff_import_roster_source_status @default(EMPTY_ENUM_VALUE)
  roles_id                   Int?
  birthdate                  DateTime?                              @db.Date
  gender                     club_staff_import_gender?
  first                      String?                                @db.VarChar(40)
  last                       String?                                @db.VarChar(40)
  phoneh                     String?                                @db.VarChar(20)
  phonec                     String?                                @db.VarChar(20)
  email                      String?                                @db.VarChar(255)
  haddress1                  String?                                @db.VarChar(100)
  haddress2                  String?                                @db.VarChar(50)
  hcity                      String?                                @db.VarChar(50)
  hstate                     String?                                @db.VarChar(2)
  hzip                       String?                                @db.VarChar(10)
  usav_id                    String?                                @db.VarChar(30)
  roster_source_last_update  DateTime                               @default(now()) @db.Timestamp(0)
  preparsed_data             String?                                @db.VarChar(1023)
  lock_roster_columns        String?                                @db.VarChar(511)
  imported_invalid_values    String?                                @db.VarChar(1023)

  @@index([club_staff_roster_id], map: "club_staff_roster_id")
  @@index([import_process_id], map: "import_process_id")
  @@index([invalid_data], map: "invalid_data")
  @@index([roster_action], map: "roster_action")
  @@index([roster_source_club_row_id], map: "roster_source_club_row_id")
  @@index([roster_source_last_update], map: "roster_source_last_update")
  @@index([roster_source_staff_row_id], map: "roster_source_staff_row_id")
  @@index([roster_source_team_row_id], map: "roster_source_team_row_id")
  @@index([tournament_id], map: "tournament_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model club_staff_master_role {
  club_staff_master_role_id Int               @id @default(autoincrement())
  club_staff_master_id      Int
  roles_id                  Int               @default(1)
  team_master_id            Int?              @default(1)
  date_modified             DateTime          @default(now()) @db.Timestamp(0)
  club_staff_master         club_staff_master @relation(fields: [club_staff_master_id], references: [club_staff_master_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_club_staff_master_role_club_staff_master_id")
  team_master               team_master?      @relation(fields: [team_master_id], references: [team_master_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_club_staff_master_role_team_master_id")

  @@index([roles_id], map: "fk_club_staff_master_role_roles_id")
  @@index([club_staff_master_id, team_master_id], map: "scm_id+team_id")
  @@index([team_master_id, roles_id, club_staff_master_role_id], map: "team_id+roles_id+csm_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model cms_page {
  cms_page_id   Int               @id @default(autoincrement())
  title         String?           @db.VarChar(255)
  text          String?           @db.MediumText
  site          cms_page_site?
  date_created  DateTime          @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified DateTime          @default(now()) @db.Timestamp(0)
  alias         String?           @db.VarChar(60)
  visible       cms_page_visible? @default(y)
}

model college_athlete_committed {
  college_athlete_committed_id Int                                  @id @default(autoincrement())
  college_program_id           Int
  athlete_master_id            Int
  users_id                     Int
  user_type                    college_athlete_committed_user_type? @default(college)
  date_modified                DateTime                             @default(now()) @db.Timestamp(0)
  status                       college_athlete_committed_status     @default(pending)
  date_created                 DateTime?                            @db.Timestamp(0)
  ip                           String?                              @db.VarChar(40)

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_program_id], map: "college_program_id")
  @@index([users_id], map: "users_id")
}

model college_athlete_evaluated {
  college_athlete_evaluated_id Int             @id @default(autoincrement())
  athlete_master_id            Int
  college_program_id           Int
  date_created                 DateTime?       @default(now()) @db.Timestamp(0)
  tag_omit                     Int             @default(0) @db.TinyInt
  date_modified                DateTime?       @default(now()) @db.Timestamp(0)
  athlete_master               athlete_master  @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_college_athlete_evaluated_athlete_master_id")
  college_program              college_program @relation(fields: [college_program_id], references: [college_program_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_college_athlete_evaluated_college_program_id")

  @@unique([athlete_master_id, college_program_id], map: "amid+cpid")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_program_id], map: "college_program_id")
  @@index([date_created], map: "dc")
  @@index([tag_omit], map: "tag")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_athlete_follow {
  college_athlete_follow_id Int                           @id @default(autoincrement())
  athlete_master_id         Int
  college_program_id        Int
  date_created              DateTime                      @default(now()) @db.Timestamp(0)
  status                    college_athlete_follow_status @default(n)
  users_id                  Int?
  tournament_id             Int?                          @default(0)
  date_modified             DateTime                      @default(now()) @db.Timestamp(0)

  @@index([athlete_master_id], map: "amid")
  @@index([athlete_master_id, college_program_id], map: "amid_cpid")
  @@index([college_program_id], map: "cpid")
  @@index([college_program_id, athlete_master_id], map: "cpid_amid")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_athlete_modified {
  college_athlete_modified_id Int      @id @default(autoincrement())
  college_program_id          Int
  athlete_master_id           Int
  date_modified               DateTime @default(now()) @db.Timestamp(0)
  descr                       String?  @db.VarChar(300)

  @@index([athlete_master_id, college_program_id], map: "amid_cpid")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_program_id], map: "college_program_id")
  @@index([college_program_id, date_modified], map: "cpid+date_modified")
  @@index([date_modified], map: "date_modified")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_camp {
  college_camp_id    Int                 @id @default(autoincrement())
  date_modified      DateTime            @default(now()) @db.Timestamp(0)
  college_program_id Int
  title              String?             @db.VarChar(255)
  description        String?             @db.MediumText
  active             college_camp_active @default(y)
  date_start         DateTime            @db.Date
  date_end           DateTime            @db.Date
  min_grade          Int
  max_grade          Int
  position           Int?                @default(0)

  @@index([college_program_id], map: "college_camp_cpid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_camp_link {
  college_camp_link_id Int      @id @default(autoincrement())
  college_camp_id      Int
  date_modified        DateTime @default(now()) @db.Timestamp(0)
  description          String?  @db.VarChar(300)
  url                  String?  @db.VarChar(300)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_camp_price {
  college_camp_price_id Int      @id @default(autoincrement())
  college_camp_id       Int
  date_modified         DateTime @default(now()) @db.Timestamp(0)
  deadline_date         DateTime @db.Date
  price                 Decimal? @db.Decimal(10, 2)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_category {
  college_category_id Int                                 @id @default(autoincrement())
  date_modified       DateTime                            @default(now()) @db.Timestamp(0)
  date_created        DateTime                            @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  college_program_id  Int
  sort_order          Int                                 @default(0) @db.TinyInt
  name                String                              @db.VarChar(100)
  limit               Int                                 @default(500)
  color               String                              @db.VarChar(10)
  total               Int                                 @default(0) @db.UnsignedInt
  task                college_category_task               @default(n)
  mailing_template_id Int?
  done_on_send        college_category_done_on_send       @default(n)
  remove_on_done      college_category_remove_on_done     @default(n)
  category_on_done    Int?
  notify_emails       String                              @default("") @db.VarChar(300)
  is_questionnaire    college_category_is_questionnaire   @default(dbgenerated("0"))
  priorities_id       Int?
  due_date            DateTime?                           @db.DateTime(0)
  description         String?                             @db.VarChar(300)
  due_date_template   college_category_due_date_template?
  date_deleted        DateTime?                           @db.Timestamp(0)

  @@unique([college_program_id, name], map: "college+name")
  @@index([date_modified], map: "date_modified")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_category_athlete {
  college_category_athlete_id Int                           @id @default(autoincrement())
  date_modified               DateTime                      @default(now()) @db.Timestamp(0)
  date_created                DateTime                      @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  college_category_id         Int
  college_program_id          Int
  athlete_master_id           Int
  sort_order                  Int?                          @db.TinyInt
  done                        college_category_athlete_done @default(n)
  assigned_to_coach           Int?
  priorities_id               Int?
  tournament_id               Int?                          @default(0)
  users_id                    Int?
  due_date                    DateTime?                     @db.DateTime(0)
  description                 String?                       @db.VarChar(300)

  @@unique([college_program_id, athlete_master_id, college_category_id], map: "college+athlete+category")
  @@index([athlete_master_id], map: "amid")
  @@index([college_category_id], map: "ccid")
  @@index([college_program_id], map: "cpid")
  @@index([sort_order], map: "sort")
}

model college_conference {
  college_conference_id       Int      @id @default(autoincrement())
  date_modified               DateTime @default(now()) @db.Timestamp(0)
  college_sanctioning_body_id Int?
  name                        String?  @unique(map: "name") @db.VarChar(100)
  url                         String?  @db.VarChar(300)

  @@index([college_sanctioning_body_id], map: "college_sanctioning_body_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_email_gateway {
  college_email_gateway_id Int      @id @default(autoincrement())
  date_modified            DateTime @default(now()) @db.Timestamp(0)
  college_program_id       Int
  athlete_master_id        Int?     @default(0)
  athlete_master_list      String?  @db.VarChar(255)
  email_file               String?  @db.VarChar(125)
  email_to                 String?  @db.VarChar(255)
  email_bcc                String?  @db.VarChar(125)
  attach_count             Int      @default(0)
  body                     String?  @db.MediumText
  from                     String?  @db.VarChar(75)
  subject                  String?  @db.VarChar(255)
  email_date               String?  @db.VarChar(100)

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([date_modified], map: "date_modified")
  @@index([college_program_id, date_modified], map: "program+date_modified")
}

model college_imessage {
  college_imessage_id Int       @id @default(autoincrement())
  college_program_id  Int?
  users_id            Int
  athlete_master_id   Int
  date_created        DateTime? @db.Timestamp(0)
  accept              Boolean?
  reject              Boolean?
  processed           DateTime? @db.Timestamp(0)

  @@index([accept], map: "accept")
  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([processed], map: "processed")
  @@index([reject], map: "reject")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_lookup_athletes {
  college_lookup_athletes_id Int      @id @default(autoincrement())
  college_program_id         Int?
  first                      String?  @db.VarChar(40)
  last                       String?  @db.VarChar(40)
  email                      String?  @db.VarChar(225)
  hzip                       String?  @db.VarChar(10)
  hcity                      String?  @db.VarChar(50)
  hstate                     String?  @db.VarChar(6)
  gradyear                   Int?     @db.SmallInt
  position                   Int?
  club_name                  String?  @db.VarChar(150)
  team_name                  String?  @db.VarChar(100)
  users_id                   Int?
  date_created               DateTime @default(now()) @db.Timestamp(0)
  found_rows                 Int
}

model college_message_template_answer {
  college_message_template_answer_id Int      @id @default(autoincrement())
  college_program_id                 Int
  title                              String?  @db.VarChar(255)
  body                               String?  @db.VarChar(2000)
  date_modified                      DateTime @default(now()) @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_note {
  college_note_id      Int                       @id @default(autoincrement())
  uuid                 String                    @unique(map: "college_note_uuid_uindex") @db.VarChar(36)
  old_college_notes_id Int                       @default(0)
  date_modified        DateTime?                 @default(now()) @db.Timestamp(0)
  last                 Boolean?
  users_id             Int
  college_program_id   Int
  athlete_roster_id    Int?
  athlete_master_id    Int?
  device_type          college_note_device_type?
  device_reg           String?                   @db.VarChar(50)
  pda_player_id        Int?
  export_pda           Boolean                   @default(true)
  tournament_id        Int                       @default(0)
  team_roster_id       Int?
  coach                String?                   @default("") @db.VarChar(2)
  to_do                Int                       @default(0)
  date_created         DateTime?                 @default(dbgenerated("'1970-01-01 00:00:01'")) @db.Timestamp(0)
  category             Int?
  title                String                    @default("") @db.VarChar(255)
  note                 String?                   @db.MediumText
  action               String?                   @db.VarChar(100)
  due_date             DateTime?                 @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  priority             college_note_priority?
  assign_to            String?                   @db.VarChar(100)
  done                 college_note_done?
  stripped_note        String?                   @db.VarChar(300)
  plain_note           String?                   @db.VarChar(2000)
  app_ver              String?                   @db.VarChar(20)
  device_hash          String?                   @db.VarChar(36)
  device_os_ver        String?                   @db.VarChar(30)
  cp_id                Int?
  source               college_note_source?
  is_evaluation        Boolean?
  is_video             Boolean?
  is_watched           Boolean?
  is_conversation      Boolean?
  is_introduction      Boolean?

  @@index([cp_id], map: "Index_1")
  @@index([athlete_master_id], map: "amid")
  @@index([athlete_master_id, college_program_id], map: "athlete_master_and_program")
  @@index([coach], map: "coach")
  @@index([college_program_id, to_do, date_modified], map: "college_note_xmlapi_index_test")
  @@index([college_program_id], map: "cpid")
  @@index([college_program_id, category], map: "cpid_category")
  @@index([college_program_id, export_pda], map: "cpid_pda")
  @@index([date_created], map: "date_created")
  @@index([date_modified], map: "date_modified")
  @@index([date_modified], map: "dm")
  @@index([export_pda], map: "export_pda")
  @@index([category], map: "idx_category")
  @@index([last, college_program_id, athlete_master_id], map: "last_program_and_athlete_master")
  @@index([note(length: 1)], map: "note1")
  @@index([college_program_id, athlete_master_id], map: "program_and_athlete_master")
  @@index([college_program_id, tournament_id, athlete_master_id], map: "program_tournament_and_athlete_master")
  @@index([athlete_roster_id], map: "roster_athletes_id")
  @@index([athlete_roster_id, college_program_id], map: "roster_athletes_id_2")
  @@index([tournament_id], map: "tournaments_id")
  @@index([users_id], map: "users_id")
}

model college_note_category_fields {
  college_note_category_fields_id Int    @id @default(autoincrement()) @db.UnsignedInt
  college_program_id              Int    @db.UnsignedInt
  name                            String @db.VarChar(100)
  sort_order                      Int?   @db.TinyInt
}

model college_player_filter {
  college_player_filter_id Int      @id @default(autoincrement())
  date_modified            DateTime @default(now()) @db.Timestamp(0)
  college_program_id       Int
  sports_id                Int      @default(2) @db.SmallInt
  gradyear                 Int      @db.SmallInt
  sport_position_id        Int      @db.SmallInt
  min_approach_jump        Int      @default(0) @db.UnsignedSmallInt

  @@index([college_program_id], map: "college_programs_id")
  @@index([gradyear], map: "gradyear")
  @@index([min_approach_jump], map: "min_approach_jump")
  @@index([sport_position_id], map: "position")
  @@index([college_program_id, sports_id, gradyear, sport_position_id, min_approach_jump], map: "search_index_1")
  @@index([sports_id], map: "sports_id")
}

model college_player_needed {
  college_player_needed_id Int                          @id @default(autoincrement())
  date_modified            DateTime                     @default(now()) @db.Timestamp(0)
  college_program_id       Int
  sports_id                Int                          @default(2) @db.SmallInt
  title                    String?                      @db.VarChar(255)
  gradyear                 Int                          @db.SmallInt
  sport_position_id        Int?                         @db.SmallInt
  height                   Int?                         @db.TinyInt
  min_gpa                  Decimal?                     @db.Decimal(4, 1)
  approach                 Int?
  active                   college_player_needed_active @default(y)
  physical_req             String?                      @default("") @db.VarChar(1024)
  skill_req                String?                      @default("") @db.VarChar(1024)
  note                     String?                      @default("") @db.VarChar(512)
  univ_college_program_id  Int?                         @default(0)

  @@index([college_program_id], map: "college_program_id")
  @@index([gradyear], map: "gradyear")
  @@index([sport_position_id], map: "position")
  @@index([college_program_id, sports_id, gradyear, sport_position_id], map: "search_index_1")
  @@index([sports_id], map: "sports_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_program {
  college_program_id          Int                                       @id @default(autoincrement())
  old_college_programs_id     Int?                                      @default(0)
  date_created                DateTime?                                 @default(now()) @db.Timestamp(0)
  date_modified               DateTime                                  @default(now()) @db.Timestamp(0)
  college_id                  Int?
  sports_id                   Int?                                      @db.SmallInt
  region_code                 String?                                   @db.VarChar(5)
  college_sanctioning_body_id Int?                                      @default(0)
  college_conference_id       Int?                                      @default(0)
  mascot                      String?                                   @db.VarChar(50)
  common_names                String?                                   @db.VarChar(150)
  short_name                  String?                                   @db.VarChar(10)
  street                      String?                                   @db.VarChar(50)
  street2                     String?                                   @db.VarChar(50)
  city                        String?                                   @db.VarChar(50)
  state                       String?                                   @db.VarChar(2)
  zip                         String?                                   @db.VarChar(10)
  program_url                 String?                                   @db.VarChar(150)
  camp_url                    String?                                   @db.VarChar(150)
  univ_id                     Int?
  blocked                     college_program_blocked?                  @default(n)
  send_sms_updates            college_program_send_sms_updates?         @default(y)
  membership                  college_program_membership?               @default(free)
  tier                        String?                                   @default("") @db.VarChar(1)
  tier_expires                DateTime?                                 @db.Timestamp(0)
  default_colors              String?                                   @db.MediumText
  emails_sending_limit        Int?                                      @default(0)
  contacts_per_gradyear_limit Int                                       @default(3000)
  emails_sent_count           Int?                                      @default(0)
  link_home                   String?                                   @default("") @db.VarChar(550)
  link_news                   String?                                   @default("") @db.VarChar(550)
  link_camps                  String?                                   @default("") @db.VarChar(550)
  link_roster                 String?                                   @default("") @db.VarChar(550)
  link_schedule               String?                                   @default("") @db.VarChar(550)
  link_facebook               String?                                   @default("") @db.VarChar(550)
  link_instagram              String?                                   @default("") @db.VarChar(550)
  link_twitter                String?                                   @default("") @db.VarChar(550)
  link_snapchat               String?                                   @default("") @db.VarChar(550)
  link_questionnaire          String?                                   @default("") @db.VarChar(550)
  desc_heart                  String?                                   @default("") @db.VarChar(1024)
  desc_diamond                String?                                   @default("") @db.VarChar(1024)
  desc_club                   String?                                   @default("") @db.VarChar(1024)
  desc_spade                  String?                                   @default("") @db.VarChar(1024)
  desc_star                   String?                                   @default("") @db.VarChar(1024)
  desc_group                  String?                                   @default("") @db.VarChar(1024)
  email_prefix                String?                                   @default("") @db.VarChar(30)
  google_spreadsheets_url     String?                                   @default("") @db.VarChar(150)
  allow_email_templates       college_program_allow_email_templates?    @default(n)
  customer_id                 String?                                   @db.VarChar(30)
  card_info                   String?                                   @db.VarChar(30)
  stripe_data                 String?                                   @db.MediumText
  stripe_card_id              String?                                   @db.VarChar(30)
  stripe_card_exp_date        DateTime?                                 @db.Date
  sendgrid_account_id         Int?
  eval_quota                  Int?                                      @default(300)
  eval_count                  Int?                                      @default(0)
  admissions_website          String?                                   @default("") @db.VarChar(1024)
  reply_to_questionnaire      String?                                   @db.VarChar(512)
  camp_url_questionnaire      String?                                   @db.VarChar(1024)
  staff_id_questionnaire      Int?
  ac_subscription             college_program_ac_subscription?
  ac_expires                  DateTime?                                 @db.Timestamp(0)
  allow_pay_by_check          college_program_allow_pay_by_check?       @default(y)
  access_only_receipts        college_program_access_only_receipts?     @default(n)
  usa_ntdp                    college_program_usa_ntdp?                 @default(n)
  cross_program_visibility    college_program_cross_program_visibility?
  use_new_profile             college_program_use_new_profile?          @default(y)
  migrate_to_new_profile      college_program_migrate_to_new_profile?   @default(done)
  admin_email_sent            admin_email_sent[]
  college_athlete_evaluated   college_athlete_evaluated[]

  @@index([college_conference_id], map: "college_conference_id")
  @@index([college_id], map: "college_id")
  @@index([college_sanctioning_body_id], map: "college_sanctioning_body_id")
  @@index([sports_id, college_id], map: "sport_and_college")
  @@index([sports_id], map: "sports_id")
  @@index([univ_id], map: "univ_id")
}

model college_program_delete_log {
  id                 Int    @id @default(autoincrement())
  college_program_id Int
  user               String @db.VarChar(255)
  unix_time          Int
}

model college_questionnaire_sent {
  college_questionnaire_sent_id Int                                  @id @default(autoincrement())
  athlete_master_id             Int
  name                          String?                              @default("") @db.VarChar(255)
  email                         String                               @db.VarChar(255)
  recipient                     college_questionnaire_sent_recipient @default(athlete)
  status                        String?                              @db.VarChar(15)
  date_created                  DateTime?                            @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  tournament_id                 Int?                                 @default(0)
  college_program_id            Int
  college_user_id               Int
  club_staff_master_id          Int?
  date_modified                 DateTime?                            @default(now()) @db.Timestamp(0)
  sg_message_id                 String?                              @db.VarChar(100)
  college_note_id               Int?
  date_responded                DateTime?                            @db.Timestamp(0)

  @@index([college_note_id], map: "cnid")
  @@index([college_user_id], map: "college_user_id")
  @@index([college_program_id], map: "cpid")
  @@index([athlete_master_id], map: "cqs_athlete_master_id")
  @@index([email(length: 191)], map: "cqs_email")
  @@index([date_created], map: "date_created")
  @@index([recipient], map: "recipient")
  @@index([status], map: "status")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_rank {
  college_rank_id       Int                              @id @default(autoincrement())
  date_modified         DateTime                         @default(now()) @db.Timestamp(0)
  date_deleted          DateTime?                        @db.Timestamp(0)
  college_program_id    Int
  rank_order            Int                              @db.TinyInt
  title                 String                           @db.VarChar(255)
  alias                 String?                          @db.VarChar(2)
  description           String?                          @default("") @db.VarChar(300)
  type_rank             String?                          @default("any") @db.VarChar(10)
  type_value            Int?                             @db.TinyInt
  values                String?                          @db.VarChar(1024)
  college_rank_group_id Int?                             @default(0)
  multiple_per_coach    college_rank_multiple_per_coach? @default(n)
  is_primary            college_rank_is_primary?         @default(n)
  default_sports        String?                          @db.Text

  @@index([college_program_id], map: "cpid")
  @@index([college_rank_group_id], map: "crgid")
  @@index([date_deleted], map: "dd")
  @@index([date_modified], map: "dm")
  @@index([rank_order], map: "rank_order")
  @@index([title(length: 191)], map: "title")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_rank_archive {
  college_rank_archive_id Int                               @id @default(autoincrement())
  college_rank_history_id Int                               @default(0)
  college_rank_master_id  Int?
  old_college_ranks_id    Int                               @default(0)
  date_modified           DateTime                          @default(now()) @db.Timestamp(0)
  athlete_roster_id       Int                               @default(0)
  athlete_master_id       Int
  device_type             college_rank_archive_device_type?
  device_reg              String?                           @db.VarChar(50)
  pda_player_id           Int?
  college_program_id      Int                               @default(0)
  users_id                Int                               @default(0)
  tag_heart               Int                               @default(0) @db.TinyInt
  tag_diamond             Int                               @default(0) @db.TinyInt
  tag_club                Int                               @default(0) @db.TinyInt
  tag_spade               Int                               @default(0) @db.TinyInt
  tag_omit                Int                               @default(0) @db.TinyInt
  rank                    String                            @default("") @db.VarChar(7)
  done                    college_rank_archive_done?
  short_desc              String                            @default("") @db.VarChar(100)
  coach                   String                            @default("") @db.VarChar(2)
  tag_star                Int                               @default(0) @db.TinyInt
  recruit                 Boolean                           @default(false)
  tournament_id           Int                               @default(0)
  date_created            DateTime                          @default(dbgenerated("'0000-00-00 00:00:00'")) @db.DateTime(0)
  college_db_id           Int                               @default(0)
  deleteme                Int                               @default(0)
  import_programid        Int?
  import_playerid         Int?
  import_users_id_coach   Int?
  ip                      String?                           @default("") @db.VarChar(39)
  app_ver                 String?                           @db.VarChar(20)
  device_hash             String?                           @db.VarChar(50)
  device_os_ver           String?                           @db.VarChar(30)

  @@index([users_id], map: "users_id")
}

model college_rank_group {
  college_rank_group_id Int                         @id @default(autoincrement())
  college_program_id    Int
  title                 String                      @db.VarChar(255)
  description           String?                     @db.VarChar(300)
  sort_order            Int?                        @db.TinyInt
  date_modified         DateTime?                   @default(now()) @db.Timestamp(0)
  date_deleted          DateTime?                   @db.Timestamp(0)
  general               college_rank_group_general?
  ntdp                  college_rank_group_ntdp?
  default_sports        String?                     @db.Text

  @@index([college_program_id], map: "cpid")
  @@index([sort_order], map: "sort_order")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_rank_history {
  college_rank_history_id Int                               @id @default(autoincrement())
  college_rank_master_id  Int?
  old_college_ranks_id    Int                               @default(0)
  date_modified           DateTime                          @default(now()) @db.Timestamp(0)
  athlete_roster_id       Int                               @default(0)
  athlete_master_id       Int
  device_type             college_rank_history_device_type?
  device_reg              String?                           @db.VarChar(50)
  pda_player_id           Int?
  college_program_id      Int                               @default(0)
  users_id                Int                               @default(0)
  tag_heart               Int                               @default(0) @db.TinyInt
  tag_diamond             Int                               @default(0) @db.TinyInt
  tag_club                Int                               @default(0) @db.TinyInt
  tag_spade               Int                               @default(0) @db.TinyInt
  tag_omit                Int                               @default(0) @db.TinyInt
  rank                    String                            @default("") @db.VarChar(7)
  done                    college_rank_history_done?
  short_desc              String                            @default("") @db.VarChar(100)
  coach                   String                            @default("") @db.VarChar(2)
  tag_star                Int                               @default(0) @db.TinyInt
  recruit                 Boolean                           @default(false)
  tournament_id           Int                               @default(0)
  date_created            DateTime                          @default(dbgenerated("'0000-00-00 00:00:00'")) @db.DateTime(0)
  college_db_id           Int                               @default(0)
  deleteme                Int                               @default(0)
  import_programid        Int?
  import_playerid         Int?
  import_users_id_coach   Int?
  ip                      String?                           @default("") @db.VarChar(39)
  app_ver                 String?                           @db.VarChar(20)
  device_hash             String?                           @db.VarChar(50)
  device_os_ver           String?                           @db.VarChar(30)

  @@index([athlete_master_id], map: "athlete_master")
  @@index([coach], map: "coach")
  @@index([college_program_id, athlete_master_id], map: "college_program_and_athlete_master")
  @@index([college_program_id], map: "college_programs_id")
  @@index([date_created], map: "date_created")
  @@index([date_modified], map: "date_modified")
  @@index([college_program_id, tournament_id, athlete_master_id], map: "program_tournament_and_athlete_master")
  @@index([rank], map: "rank")
  @@index([athlete_roster_id], map: "roster_athletes_id")
  @@index([athlete_roster_id, college_program_id], map: "roster_athletes_id_2")
  @@index([tag_club], map: "tag_club")
  @@index([tag_diamond], map: "tag_diamond")
  @@index([tag_heart], map: "tag_heart")
  @@index([tag_omit], map: "tag_omit")
  @@index([tag_spade], map: "tag_spade")
  @@index([tournament_id], map: "tournaments_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_rank_master {
  college_rank_master_id Int                              @id @default(autoincrement())
  old_college_ranks_id   Int                              @default(0)
  date_modified          DateTime                         @default(now()) @db.Timestamp(0)
  athlete_roster_id      Int                              @default(0)
  athlete_master_id      Int                              @default(0)
  device_type            college_rank_master_device_type?
  device_reg             String?                          @db.VarChar(50)
  pda_player_id          Int?
  college_program_id     Int                              @default(0)
  users_id               Int                              @default(0)
  tag_heart              Int                              @default(0) @db.TinyInt
  tag_diamond            Int                              @default(0) @db.TinyInt
  tag_club               Int                              @default(0) @db.TinyInt
  tag_spade              Int                              @default(0) @db.TinyInt
  tag_omit               Int                              @default(0) @db.TinyInt
  rank                   String                           @default("") @db.VarChar(7)
  done                   college_rank_master_done?
  short_desc             String                           @default("") @db.VarChar(100)
  coach                  String                           @default("") @db.VarChar(2)
  tag_star               Int                              @default(0) @db.TinyInt
  recruit                Boolean                          @default(false)
  tournament_id          Int                              @default(0)
  date_created           DateTime                         @default(dbgenerated("'0000-00-00 00:00:00'")) @db.DateTime(0)
  college_db_id          Int                              @default(0)
  deleteme               Int                              @default(0)
  import_programid       Int?
  import_playerid        Int?
  import_users_id_coach  Int?

  @@index([athlete_master_id, college_program_id], map: "amid_cpid")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([coach], map: "coach")
  @@index([college_program_id], map: "college_programs_id")
  @@index([date_modified], map: "date_modified")
  @@index([tournament_id, college_program_id, users_id, athlete_master_id], map: "eventid_cpid_usersid_amid")
  @@index([college_program_id, athlete_master_id], map: "program_and_athlete_master")
  @@index([college_program_id, date_modified], map: "program_id+date_modified")
  @@index([rank], map: "rank")
  @@index([tag_club], map: "tag_club")
  @@index([tag_diamond], map: "tag_diamond")
  @@index([tag_heart], map: "tag_heart")
  @@index([tag_omit], map: "tag_omit")
  @@index([tag_spade], map: "tag_spade")
  @@index([tournament_id], map: "tournaments_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_sanctioning_body {
  college_sanctioning_body_id   Int      @id @default(autoincrement())
  date_modified                 DateTime @default(now()) @db.Timestamp(0)
  name                          String   @db.VarChar(100)
  short_name                    String   @db.VarChar(10)
  compressed_name               String   @db.VarChar(25)
  division                      String?  @default("") @db.VarChar(10)
  college_sanctioning_body_used Int?     @default(0) @db.TinyInt

  @@index([college_sanctioning_body_id, short_name], map: "csb_id_and_name")
  @@index([college_sanctioning_body_used], map: "csbu")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_sms_update {
  college_sms_update_id Int                        @id @default(autoincrement())
  college_program_id    Int
  college_user_id       Int
  tournament_id         Int?
  enabled               college_sms_update_enabled @default(y)

  @@index([college_user_id], map: "college_user_id")
  @@index([enabled, tournament_id, college_program_id], map: "enabled+tourn_id+program_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_sport_offered {
  ope_id       Int    @default(0) @db.MediumInt
  sport_id     Int
  sport_name   String @db.VarChar(50)
  sport_gender String @db.Char(1)

  @@index([sport_id, sport_gender, ope_id], map: "sport_and_ope")
  @@index([sport_name], map: "univ_sport")
  @@index([ope_id], map: "univ_unitid")
  @@ignore
}

model college_tag {
  college_tag_id     Int                   @id @default(autoincrement())
  date_modified      DateTime              @default(now()) @db.Timestamp(0)
  date_deleted       DateTime?             @db.Timestamp(0)
  college_program_id Int?
  title              String?               @db.VarChar(255)
  description        String?               @default("") @db.VarChar(300)
  color              String                @db.VarChar(10)
  icon               String?               @default("") @db.VarChar(30)
  primary            college_tag_primary   @default(n)
  sort_order         Int?                  @db.TinyInt
  tag_type           college_tag_tag_type?

  @@index([college_program_id], map: "cpid")
  @@index([date_deleted], map: "dd")
  @@index([date_modified], map: "dm")
  @@index([sort_order], map: "sort_order")
  @@index([title], map: "title")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_task {
  college_task_id             Int                             @id @default(autoincrement())
  date_modified               DateTime                        @default(now()) @db.Timestamp(0)
  date_deleted                DateTime?                       @db.Timestamp(0)
  college_program_id          Int?
  task                        String?                         @db.VarChar(255)
  sort_order                  Int?                            @db.TinyInt
  options                     String?                         @db.MediumText
  task_type                   String                          @default("athlete") @db.VarChar(50)
  multiple_options            college_task_multiple_options   @default(n)
  created_users_id            Int?
  priority_id                 Int?
  assigned_to_user_id         Int?
  email_template_id           Int?
  done_college_task_id        Int?
  task_name                   String?                         @db.VarChar(255)
  board_name                  String?                         @db.VarChar(255)
  mailing_template_id         Int?
  due_date_template           college_task_due_date_template?
  done_college_tag_id         Int?
  athlete_task                athlete_task[]
  college_task                college_task?                   @relation("college_taskTocollege_task", fields: [done_college_task_id], references: [college_task_id], onUpdate: Restrict, map: "fk_college_task_done_college_task_id")
  other_college_task          college_task[]                  @relation("college_taskTocollege_task")
  email_template_new          email_template_new?             @relation(fields: [email_template_id], references: [email_template_new_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_college_task_email_template_id")
  priorities                  priorities?                     @relation(fields: [priority_id], references: [priorities_id], onUpdate: NoAction, map: "fk_college_task_priority_id")
  college_task_column         college_task_column[]
  college_task_column_divider college_task_column_divider[]

  @@index([college_program_id], map: "cpid")
  @@index([date_deleted], map: "dd")
  @@index([done_college_task_id], map: "fk_college_task_done_college_task_id_idx")
  @@index([email_template_id], map: "fk_email_template_id_idx")
  @@index([priority_id], map: "priority_id_idx")
  @@index([sort_order], map: "sort_order")
  @@index([task], map: "task")
}

model college_task_column {
  college_task_column_id      Int                                   @id @default(autoincrement())
  date_created                DateTime?                             @default(now()) @db.Timestamp(0)
  date_modified               DateTime?                             @default(now()) @db.Timestamp(0)
  college_task_id             Int
  college_program_id          Int
  title                       String                                @db.VarChar(255)
  sort_order                  Int?                                  @default(0) @db.TinyInt
  dividers_enabled            college_task_column_dividers_enabled? @default(n)
  college_task                college_task                          @relation(fields: [college_task_id], references: [college_task_id], onDelete: Cascade, map: "fk_college_task_column_college_task_id")
  college_task_column_divider college_task_column_divider[]

  @@index([college_program_id], map: "college_program_id")
  @@index([college_task_id], map: "college_task_id")
}

model college_task_column_divider {
  college_task_column_divider_id Int                 @id @default(autoincrement())
  date_created                   DateTime?           @default(now()) @db.Timestamp(0)
  date_modified                  DateTime?           @default(now()) @db.Timestamp(0)
  college_task_id                Int
  college_task_column_id         Int
  college_program_id             Int
  title                          String              @db.VarChar(255)
  color                          String?             @db.VarChar(10)
  sort_order                     Int?                @default(0) @db.TinyInt
  instruction                    String?             @db.Text
  college_task_column            college_task_column @relation(fields: [college_task_column_id], references: [college_task_column_id], onDelete: Cascade, map: "fk_college_task_column_divider_college_task_column_id")
  college_task                   college_task        @relation(fields: [college_task_id], references: [college_task_id], onDelete: Cascade, map: "fk_college_task_column_divider_college_task_id")

  @@index([college_program_id], map: "college_program_id")
  @@index([college_task_id], map: "college_task_id")
  @@index([college_task_column_id], map: "fk_college_task_column_divider_college_task_column_id")
}

model college_task_status {
  college_task_status_id Int            @id @default(autoincrement())
  date_modified          DateTime       @default(now()) @db.Timestamp(0)
  date_created           DateTime?      @default(now()) @db.Timestamp(0)
  college_program_id     Int?
  name                   String?        @db.VarChar(100)
  sort_order             Int?           @db.TinyInt
  athlete_task           athlete_task[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model college_user {
  college_user_id            Int                                     @id @default(autoincrement())
  old_college_staff_id       Int                                     @default(0)
  date_modified              DateTime                                @default(now()) @db.Timestamp(0)
  users_id                   Int                                     @default(0)
  college_program_id         Int                                     @default(0)
  ncsa_coach_id              Int?
  active_staff               college_user_active_staff               @default(y)
  roles_id                   Int                                     @default(0)
  rcv_recruit_email          college_user_rcv_recruit_email          @default(y)
  staff_email                String                                  @default("") @db.VarChar(75)
  email_alternate            String?                                 @default("") @db.VarChar(75)
  gender                     String                                  @default("") @db.VarChar(1)
  cell_phone                 String?                                 @db.VarChar(15)
  cell_provider              String?                                 @db.VarChar(125)
  wireless_provider_id       Int                                     @default(0)
  cell_phone_verified        college_user_cell_phone_verified        @default(n)
  cell_verif_code            String?                                 @db.VarChar(10)
  device_model               String?                                 @db.VarChar(125)
  device_os                  String?                                 @db.VarChar(125)
  device_version             String?                                 @db.VarChar(125)
  used_for_ua                college_user_used_for_ua                @default(n)
  show_cell_phone            college_user_show_cell_phone            @default(n)
  show_cell_phone_to_coaches college_user_show_cell_phone_to_coaches @default(y)
  home_phone                 String?                                 @db.VarChar(15)
  show_home_phone            college_user_show_home_phone            @default(n)
  work_phone                 String?                                 @db.VarChar(15)
  show_work_phone            college_user_show_work_phone            @default(y)
  other_interests            String?                                 @db.Text
  biography                  String?                                 @db.MediumText
  link_to_bio                String?                                 @db.VarChar(550)
  mailing_address            String?                                 @db.VarChar(150)
  sort_order                 Int?                                    @db.TinyInt
  update_by                  DateTime?                               @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  update_from                String?                                 @db.VarChar(20)
  import_uid                 Int?
  import_role                String?                                 @db.VarChar(50)
  email_gateway              String?                                 @db.VarChar(125)
  coach                      String?                                 @db.VarChar(2)
  message_notify             college_user_message_notify?            @default(notify)
  layout_version             college_user_layout_version?            @default(EMPTY_ENUM_VALUE)
  portrait                   String?                                 @db.VarChar(255)
  initials                   String?                                 @default("") @db.VarChar(2)
  color                      String?                                 @db.VarChar(10)
  recruiter_usa_ntdp         college_user_recruiter_usa_ntdp?        @default(n)
  fr_api_token               String?                                 @db.VarChar(20)
  fr_coach_data              String?                                 @db.MediumText
  ntdp_staff                 college_user_ntdp_staff?                @default(n)
  is_evaluator               college_user_is_evaluator               @default(n)
  date_deleted               DateTime?                               @db.Timestamp(0)

  @@unique([old_college_staff_id, college_user_id], map: "old_db_id_and_college_user_id")
  @@index([email_alternate], map: "email_alternate")
  @@index([email_gateway, college_user_id], map: "email_gateway+college_user_id")
  @@index([import_uid], map: "import_uid")
  @@index([staff_email], map: "staff_email")
  @@index([college_program_id], map: "ua_college_program_FK")
  @@index([users_id], map: "user_FK")
}

model college_user_athlete_metrics {
  college_user_athlete_metrics_id Int                                            @id @default(autoincrement())
  college_user_metrics_id         Int
  athlete_master_id               Int
  has_athlete_tags                college_user_athlete_metrics_has_athlete_tags  @default(n)
  has_college_notes               college_user_athlete_metrics_has_college_notes @default(n)
  has_athlete_ranks               college_user_athlete_metrics_has_athlete_ranks @default(n)
  has_evaluation                  college_user_athlete_metrics_has_evaluation    @default(n)
  has_frontrush_log               college_user_athlete_metrics_has_frontrush_log @default(n)
  college_user_metrics            college_user_metrics                           @relation(fields: [college_user_metrics_id], references: [college_user_metrics_id], onDelete: Cascade, onUpdate: NoAction, map: "college_user_athlete_metrics_college_user_metrics_FK")

  @@unique([college_user_metrics_id, athlete_master_id], map: "college_user_athlete_metrics_college_user_metrics_id_IDX")
}

model college_user_email {
  college_user_email_id Int       @id @default(autoincrement())
  college_program_id    Int?
  college_user_id       Int?
  athlete_master_id     Int?
  first                 String?   @db.VarChar(40)
  last                  String?   @db.VarChar(40)
  date_sent             DateTime? @default(now()) @db.Timestamp(0)
  email                 String?   @db.VarChar(60)
  title                 String?   @db.VarChar(255)
  body                  String?   @db.MediumText
  private_message       String?   @db.MediumText
  athlete_email_sent_id Int?

  @@index([athlete_email_sent_id], map: "Index_1")
  @@index([athlete_master_id, college_user_id], map: "athlete_master+college_user")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_program_id], map: "college_program")
  @@index([college_user_id], map: "college_user")
  @@index([date_sent], map: "date_sent")
}

model college_user_metrics {
  college_user_metrics_id            Int                            @id @default(autoincrement())
  year_month_created                 Int
  college_program_id                 Int
  users_id                           Int
  college_user_id                    Int?
  username                           String?                        @db.VarChar(255)
  college_name                       String?                        @db.VarChar(255)
  sports_id                          Int?                           @db.SmallInt
  last_login                         DateTime?                      @db.DateTime(0)
  athlete_tags_total                 Int?                           @default(0)
  college_notes_total                Int?                           @default(0)
  athlete_rank_total                 Int?                           @default(0)
  evaluated_athletes_total           Int?                           @default(0)
  web_athlete_profile_requests_total Int?                           @default(0)
  app_athlete_profile_requests_total Int?                           @default(0)
  cp_downloads_total                 Int?                           @default(0)
  cp_exports_total                   Int?                           @default(0)
  frontrush_total                    Int?                           @default(0)
  college_user_athlete_metrics       college_user_athlete_metrics[]

  @@unique([year_month_created, college_program_id, users_id], map: "UIX_YEAR_MONTH_CP_USERS")
}

model content {
  content_id       Int       @id @default(autoincrement())
  content_type     String?   @db.VarChar(50)
  parent_id        Int       @default(0)
  title            String    @db.VarChar(255)
  breadcrumbs      String?   @db.VarChar(250)
  intro            String?   @db.MediumText
  content          String    @db.MediumText
  meta_keywords    String?   @db.MediumText
  meta_description String?   @db.MediumText
  author_id        Int       @default(0)
  date_modified    DateTime  @default(now()) @db.Timestamp(0)
  published        Int?
  date_published   DateTime? @db.Timestamp(0)
  published_from   DateTime? @db.Timestamp(0)
  published_to     DateTime? @db.Timestamp(0)
  properties       String?   @db.MediumText
  hits             Int?
  filepath         String?   @db.VarChar(250)

  @@index([content_id, content_type], map: "content_id_and_type")
}

model custom_field {
  custom_field_id    Int               @id @default(autoincrement()) @db.UnsignedInt
  college_program_id Int               @db.UnsignedInt
  name               String            @db.VarChar(100)
  type               custom_field_type
  values             String?           @default("") @db.VarChar(50)
  sort_order         Int?              @db.TinyInt

  @@index([college_program_id], map: "college_program")
}

model custom_value {
  custom_value_id   Int    @id @default(autoincrement())
  custom_field_id   Int
  athlete_master_id Int
  value             String @db.VarChar(1024)

  @@index([athlete_master_id, custom_field_id], map: "athlete_master_id+custom_field_id")
  @@index([custom_field_id], map: "custom_field_id")
}

model debug_request_cp_app {
  debug_request_cp_app_id Int       @id @default(autoincrement())
  tournament_id           Int?
  users_id                Int
  college_program_id      Int?
  date_added              DateTime? @db.Timestamp(0)
  date_sent               DateTime? @db.Timestamp(0)

  @@index([college_program_id], map: "cpid")
  @@index([date_added], map: "date_added")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "users_id")
}

model eapi_college_provider {
  eapi_college_provider_id Int       @id @default(autoincrement())
  college_program_id       Int
  eapi_provider_id         Int
  deleted                  DateTime? @db.DateTime(0)
  date_modified            DateTime  @default(now()) @db.Timestamp(0)

  @@unique([eapi_provider_id, college_program_id], map: "unique_provider_id_college_program_id")
}

model eapi_log_request {
  eapi_log_request_id Int       @id @default(autoincrement())
  api_key             String    @db.VarChar(45)
  url                 String?   @default("") @db.VarChar(45)
  query               String?   @default("") @db.VarChar(255)
  ip                  String?   @db.VarChar(15)
  date_created        DateTime? @default(now()) @db.Timestamp(0)
  response_code       Int?
  response_time       Int?
}

model eapi_provider {
  eapi_provider_id Int       @id @default(autoincrement())
  api_key          String    @db.VarChar(45)
  deleted          DateTime? @db.DateTime(0)
  date_modified    DateTime  @default(now()) @db.Timestamp(0)
  name             String    @db.VarChar(128)
}

model eapi_sync {
  eapi_sync_id  Int       @id @default(autoincrement())
  ua_table_name String    @db.VarChar(255)
  changes_count Int
  date_created  DateTime? @default(now()) @db.Timestamp(0)
  status        String    @db.VarChar(16)
  offset        Int?      @default(0)
  college_id    Int?

  @@index([college_id], map: "college_id")
  @@index([status], map: "index_status")
  @@index([ua_table_name(length: 191)], map: "index_ua_table_name")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model education_finance_data {
  LEAID        String   @db.VarChar(7)
  CENSUSID     String?  @db.VarChar(14)
  PID6         String?  @db.VarChar(6)
  UNIT_TYPE    String?  @db.VarChar(10)
  FIPST        String?  @db.VarChar(2)
  CONUM        String?  @db.VarChar(3)
  CSA          String?  @db.VarChar(3)
  CBSA         String?  @db.VarChar(5)
  NAME         String?  @db.VarChar(100)
  STNAME       String?  @db.VarChar(50)
  STABBR       String?  @db.Char(2)
  SCHLEV       String?  @db.Char(2)
  AGCHRT       String?  @db.Char(1)
  YEAR         String   @db.Char(4)
  CCDNF        String?  @db.Char(1)
  CENFILE      String?  @db.Char(1)
  GSLO         String?  @db.VarChar(2)
  GSHI         String?  @db.VarChar(2)
  V33          Decimal? @db.Decimal(15, 2)
  MEMBERSCH    Decimal? @db.Decimal(15, 2)
  TOTALREV     Decimal? @db.Decimal(15, 2)
  TFEDREV      Decimal? @db.Decimal(15, 2)
  C14          Decimal? @db.Decimal(15, 2)
  C15          Decimal? @db.Decimal(15, 2)
  C19          Decimal? @db.Decimal(15, 2)
  C22          Decimal? @db.Decimal(15, 2)
  C23          Decimal? @db.Decimal(15, 2)
  C26          Decimal? @db.Decimal(15, 2)
  C27          Decimal? @db.Decimal(15, 2)
  B11          Decimal? @db.Decimal(15, 2)
  C20          Decimal? @db.Decimal(15, 2)
  C25          Decimal? @db.Decimal(15, 2)
  C36          Decimal? @db.Decimal(15, 2)
  B10          Decimal? @db.Decimal(15, 2)
  B12          Decimal? @db.Decimal(15, 2)
  B14          Decimal? @db.Decimal(15, 2)
  B13          Decimal? @db.Decimal(15, 2)
  TSTREV       Decimal? @db.Decimal(15, 2)
  C01          Decimal? @db.Decimal(15, 2)
  C04          Decimal? @db.Decimal(15, 2)
  C05          Decimal? @db.Decimal(15, 2)
  C06          Decimal? @db.Decimal(15, 2)
  C07          Decimal? @db.Decimal(15, 2)
  C08          Decimal? @db.Decimal(15, 2)
  C09          Decimal? @db.Decimal(15, 2)
  C10          Decimal? @db.Decimal(15, 2)
  C11          Decimal? @db.Decimal(15, 2)
  C12          Decimal? @db.Decimal(15, 2)
  C13          Decimal? @db.Decimal(15, 2)
  C35          Decimal? @db.Decimal(15, 2)
  C38          Decimal? @db.Decimal(15, 2)
  C39          Decimal? @db.Decimal(15, 2)
  TLOCREV      Decimal? @db.Decimal(15, 2)
  T02          Decimal? @db.Decimal(15, 2)
  T06          Decimal? @db.Decimal(15, 2)
  T09          Decimal? @db.Decimal(15, 2)
  T15          Decimal? @db.Decimal(15, 2)
  T40          Decimal? @db.Decimal(15, 2)
  T99          Decimal? @db.Decimal(15, 2)
  D11          Decimal? @db.Decimal(15, 2)
  D23          Decimal? @db.Decimal(15, 2)
  A07          Decimal? @db.Decimal(15, 2)
  A08          Decimal? @db.Decimal(15, 2)
  A09          Decimal? @db.Decimal(15, 2)
  A11          Decimal? @db.Decimal(15, 2)
  A13          Decimal? @db.Decimal(15, 2)
  A15          Decimal? @db.Decimal(15, 2)
  A20          Decimal? @db.Decimal(15, 2)
  A40          Decimal? @db.Decimal(15, 2)
  U11          Decimal? @db.Decimal(15, 2)
  U22          Decimal? @db.Decimal(15, 2)
  U30          Decimal? @db.Decimal(15, 2)
  U50          Decimal? @db.Decimal(15, 2)
  U97          Decimal? @db.Decimal(15, 2)
  C24          Decimal? @db.Decimal(15, 2)
  TOTALEXP     Decimal? @db.Decimal(15, 2)
  TCURELSC     Decimal? @db.Decimal(15, 2)
  TCURINST     Decimal? @db.Decimal(15, 2)
  E13          Decimal? @db.Decimal(15, 2)
  V91          Decimal? @db.Decimal(15, 2)
  V92          Decimal? @db.Decimal(15, 2)
  TCURSSVC     Decimal? @db.Decimal(15, 2)
  E17          Decimal? @db.Decimal(15, 2)
  E07          Decimal? @db.Decimal(15, 2)
  E08          Decimal? @db.Decimal(15, 2)
  E09          Decimal? @db.Decimal(15, 2)
  V40          Decimal? @db.Decimal(15, 2)
  V45          Decimal? @db.Decimal(15, 2)
  V90          Decimal? @db.Decimal(15, 2)
  V85          Decimal? @db.Decimal(15, 2)
  TCUROTH      Decimal? @db.Decimal(15, 2)
  E11          Decimal? @db.Decimal(15, 2)
  V60          Decimal? @db.Decimal(15, 2)
  V65          Decimal? @db.Decimal(15, 2)
  TNONELSE     Decimal? @db.Decimal(15, 2)
  V70          Decimal? @db.Decimal(15, 2)
  V75          Decimal? @db.Decimal(15, 2)
  V80          Decimal? @db.Decimal(15, 2)
  TCAPOUT      Decimal? @db.Decimal(15, 2)
  F12          Decimal? @db.Decimal(15, 2)
  G15          Decimal? @db.Decimal(15, 2)
  K09          Decimal? @db.Decimal(15, 2)
  K10          Decimal? @db.Decimal(15, 2)
  K11          Decimal? @db.Decimal(15, 2)
  L12          Decimal? @db.Decimal(15, 2)
  M12          Decimal? @db.Decimal(15, 2)
  Q11          Decimal? @db.Decimal(15, 2)
  I86          Decimal? @db.Decimal(15, 2)
  Z32          Decimal? @db.Decimal(15, 2)
  Z33          Decimal? @db.Decimal(15, 2)
  Z35          Decimal? @db.Decimal(15, 2)
  Z36          Decimal? @db.Decimal(15, 2)
  Z37          Decimal? @db.Decimal(15, 2)
  Z38          Decimal? @db.Decimal(15, 2)
  V11          Decimal? @db.Decimal(15, 2)
  V13          Decimal? @db.Decimal(15, 2)
  V15          Decimal? @db.Decimal(15, 2)
  V17          Decimal? @db.Decimal(15, 2)
  V21          Decimal? @db.Decimal(15, 2)
  V23          Decimal? @db.Decimal(15, 2)
  V37          Decimal? @db.Decimal(15, 2)
  V29          Decimal? @db.Decimal(15, 2)
  Z34          Decimal? @db.Decimal(15, 2)
  V10          Decimal? @db.Decimal(15, 2)
  V12          Decimal? @db.Decimal(15, 2)
  V14          Decimal? @db.Decimal(15, 2)
  V16          Decimal? @db.Decimal(15, 2)
  V18          Decimal? @db.Decimal(15, 2)
  V22          Decimal? @db.Decimal(15, 2)
  V24          Decimal? @db.Decimal(15, 2)
  V38          Decimal? @db.Decimal(15, 2)
  V30          Decimal? @db.Decimal(15, 2)
  V32          Decimal? @db.Decimal(15, 2)
  V93          Decimal? @db.Decimal(15, 2)
  H            Decimal? @map("_19H") @db.Decimal(15, 2)
  F_21         Decimal? @map("_21F") @db.Decimal(15, 2)
  F_31         Decimal? @map("_31F") @db.Decimal(15, 2)
  F_41         Decimal? @map("_41F") @db.Decimal(15, 2)
  V_61         Decimal? @map("_61V") @db.Decimal(15, 2)
  V_66         Decimal? @map("_66V") @db.Decimal(15, 2)
  W01          Decimal? @db.Decimal(15, 2)
  W31          Decimal? @db.Decimal(15, 2)
  W61          Decimal? @db.Decimal(15, 2)
  V95          Decimal? @db.Decimal(15, 2)
  V02          Decimal? @db.Decimal(15, 2)
  K14          Decimal? @db.Decimal(15, 2)
  CE1          Decimal? @db.Decimal(15, 2)
  CE2          Decimal? @db.Decimal(15, 2)
  CE3          Decimal? @db.Decimal(15, 2)
  SE1          Decimal? @db.Decimal(15, 2)
  SE2          Decimal? @db.Decimal(15, 2)
  SE3          Decimal? @db.Decimal(15, 2)
  SE4          Decimal? @db.Decimal(15, 2)
  SE5          Decimal? @db.Decimal(15, 2)
  AR1          Decimal? @db.Decimal(15, 2)
  AR1A         Decimal? @db.Decimal(15, 2)
  AR1B         Decimal? @db.Decimal(15, 2)
  AR2          Decimal? @db.Decimal(15, 2)
  AR2A         Decimal? @db.Decimal(15, 2)
  AR3          Decimal? @db.Decimal(15, 2)
  AR6          Decimal? @db.Decimal(15, 2)
  AR6A         Decimal? @db.Decimal(15, 2)
  AE1          Decimal? @db.Decimal(15, 2)
  AE1A         Decimal? @db.Decimal(15, 2)
  AE1B         Decimal? @db.Decimal(15, 2)
  AE1C         Decimal? @db.Decimal(15, 2)
  AE1D         Decimal? @db.Decimal(15, 2)
  AE1E         Decimal? @db.Decimal(15, 2)
  AE1F         Decimal? @db.Decimal(15, 2)
  AE1G         Decimal? @db.Decimal(15, 2)
  AE2          Decimal? @db.Decimal(15, 2)
  AE2A         Decimal? @db.Decimal(15, 2)
  AE2B         Decimal? @db.Decimal(15, 2)
  AE2C         Decimal? @db.Decimal(15, 2)
  AE2D         Decimal? @db.Decimal(15, 2)
  AE2E         Decimal? @db.Decimal(15, 2)
  AE2F         Decimal? @db.Decimal(15, 2)
  AE2G         Decimal? @db.Decimal(15, 2)
  AE3          Decimal? @db.Decimal(15, 2)
  AE4          Decimal? @db.Decimal(15, 2)
  AE4A         Decimal? @db.Decimal(15, 2)
  AE4B         Decimal? @db.Decimal(15, 2)
  AE4C         Decimal? @db.Decimal(15, 2)
  AE4D         Decimal? @db.Decimal(15, 2)
  AE4E         Decimal? @db.Decimal(15, 2)
  AE4F         Decimal? @db.Decimal(15, 2)
  AE4G         Decimal? @db.Decimal(15, 2)
  AE5          Decimal? @db.Decimal(15, 2)
  AE6          Decimal? @db.Decimal(15, 2)
  AE7          Decimal? @db.Decimal(15, 2)
  AE8          Decimal? @db.Decimal(15, 2)
  WEIGHT       Decimal? @db.Decimal(15, 2)
  FL_V33       String?  @db.Char(1)
  FL_MEMBERSCH String?  @db.Char(1)
  FL_C14       String?  @db.Char(1)
  FL_C15       String?  @db.Char(1)
  FL_C19       String?  @db.Char(1)
  FL_C22       String?  @db.Char(1)
  FL_C23       String?  @db.Char(1)
  FL_C26       String?  @db.Char(1)
  FL_C27       String?  @db.Char(1)
  FL_B11       String?  @db.Char(1)
  FL_C20       String?  @db.Char(1)
  FL_C25       String?  @db.Char(1)
  FL_C36       String?  @db.Char(1)
  FL_B10       String?  @db.Char(1)
  FL_B12       String?  @db.Char(1)
  FL_B14       String?  @db.Char(1)
  FL_B13       String?  @db.Char(1)
  FL_C01       String?  @db.Char(1)
  FL_C04       String?  @db.Char(1)
  FL_C05       String?  @db.Char(1)
  FL_C06       String?  @db.Char(1)
  FL_C07       String?  @db.Char(1)
  FL_C08       String?  @db.Char(1)
  FL_C09       String?  @db.Char(1)
  FL_C10       String?  @db.Char(1)
  FL_C11       String?  @db.Char(1)
  FL_C12       String?  @db.Char(1)
  FL_C13       String?  @db.Char(1)
  FL_C35       String?  @db.Char(1)
  FL_C38       String?  @db.Char(1)
  FL_C39       String?  @db.Char(1)
  FL_T02       String?  @db.Char(1)
  FL_T06       String?  @db.Char(1)
  FL_T09       String?  @db.Char(1)
  FL_T15       String?  @db.Char(1)
  FL_T40       String?  @db.Char(1)
  FL_T99       String?  @db.Char(1)
  FL_D11       String?  @db.Char(1)
  FL_D23       String?  @db.Char(1)
  FL_A07       String?  @db.Char(1)
  FL_A08       String?  @db.Char(1)
  FL_A09       String?  @db.Char(1)
  FL_A11       String?  @db.Char(1)
  FL_A13       String?  @db.Char(1)
  FL_A15       String?  @db.Char(1)
  FL_A20       String?  @db.Char(1)
  FL_A40       String?  @db.Char(1)
  FL_U11       String?  @db.Char(1)
  FL_U22       String?  @db.Char(1)
  FL_U30       String?  @db.Char(1)
  FL_U50       String?  @db.Char(1)
  FL_U97       String?  @db.Char(1)
  FL_C24       String?  @db.Char(1)
  FL_E13       String?  @db.Char(1)
  FL_V91       String?  @db.Char(1)
  FL_V92       String?  @db.Char(1)
  FL_E17       String?  @db.Char(1)
  FL_E07       String?  @db.Char(1)
  FL_E08       String?  @db.Char(1)
  FL_E09       String?  @db.Char(1)
  FL_V40       String?  @db.Char(1)
  FL_V45       String?  @db.Char(1)
  FL_V90       String?  @db.Char(1)
  FL_V85       String?  @db.Char(1)
  FL_E11       String?  @db.Char(1)
  FL_V60       String?  @db.Char(1)
  FL_V65       String?  @db.Char(1)
  FL_V70       String?  @db.Char(1)
  FL_V75       String?  @db.Char(1)
  FL_V80       String?  @db.Char(1)
  FL_F12       String?  @db.Char(1)
  FL_G15       String?  @db.Char(1)
  FL_K09       String?  @db.Char(1)
  FL_K10       String?  @db.Char(1)
  FL_K11       String?  @db.Char(1)
  FL_L12       String?  @db.Char(1)
  FL_M12       String?  @db.Char(1)
  FL_Q11       String?  @db.Char(1)
  FL_I86       String?  @db.Char(1)
  FL_Z32       String?  @db.Char(1)
  FL_Z33       String?  @db.Char(1)
  FL_Z35       String?  @db.Char(1)
  FL_Z36       String?  @db.Char(1)
  FL_Z37       String?  @db.Char(1)
  FL_Z38       String?  @db.Char(1)
  FL_V11       String?  @db.Char(1)
  FL_V13       String?  @db.Char(1)
  FL_V15       String?  @db.Char(1)
  FL_V17       String?  @db.Char(1)
  FL_V21       String?  @db.Char(1)
  FL_V23       String?  @db.Char(1)
  FL_V37       String?  @db.Char(1)
  FL_V29       String?  @db.Char(1)
  FL_Z34       String?  @db.Char(1)
  FL_V10       String?  @db.Char(1)
  FL_V12       String?  @db.Char(1)
  FL_V14       String?  @db.Char(1)
  FL_V16       String?  @db.Char(1)
  FL_V18       String?  @db.Char(1)
  FL_V22       String?  @db.Char(1)
  FL_V24       String?  @db.Char(1)
  FL_V38       String?  @db.Char(1)
  FL_V30       String?  @db.Char(1)
  FL_V32       String?  @db.Char(1)
  FL_V93       String?  @db.Char(1)
  FL_19H       String?  @db.Char(1)
  FL_21F       String?  @db.Char(1)
  FL_31F       String?  @db.Char(1)
  FL_41F       String?  @db.Char(1)
  FL_61V       String?  @db.Char(1)
  FL_66V       String?  @db.Char(1)
  FL_W01       String?  @db.Char(1)
  FL_W31       String?  @db.Char(1)
  FL_W61       String?  @db.Char(1)
  FL_V95       String?  @db.Char(1)
  FL_V02       String?  @db.Char(1)
  FL_K14       String?  @db.Char(1)
  FL_CE1       String?  @db.Char(1)
  FL_CE2       String?  @db.Char(1)
  FL_CE3       String?  @db.Char(1)
  FL_SE1       String?  @db.Char(1)
  FL_SE2       String?  @db.Char(1)
  FL_SE3       String?  @db.Char(1)
  FL_SE4       String?  @db.Char(1)
  FL_SE5       String?  @db.Char(1)
  FL_AR1       String?  @db.Char(1)
  FL_AR1A      String?  @db.Char(1)
  FL_AR1B      String?  @db.Char(1)
  FL_AR2       String?  @db.Char(1)
  FL_AR2A      String?  @db.Char(1)
  FL_AR3       String?  @db.Char(1)
  FL_AR6       String?  @db.Char(1)
  FL_AR6A      String?  @db.Char(1)
  FL_AE1       String?  @db.Char(1)
  FL_AE1A      String?  @db.Char(1)
  FL_AE1B      String?  @db.Char(1)
  FL_AE1C      String?  @db.Char(1)
  FL_AE1D      String?  @db.Char(1)
  FL_AE1E      String?  @db.Char(1)
  FL_AE1F      String?  @db.Char(1)
  FL_AE1G      String?  @db.Char(1)
  FL_AE2       String?  @db.Char(1)
  FL_AE2A      String?  @db.Char(1)
  FL_AE2B      String?  @db.Char(1)
  FL_AE2C      String?  @db.Char(1)
  FL_AE2D      String?  @db.Char(1)
  FL_AE2E      String?  @db.Char(1)
  FL_AE2F      String?  @db.Char(1)
  FL_AE2G      String?  @db.Char(1)
  FL_AE3       String?  @db.Char(1)
  FL_AE4       String?  @db.Char(1)
  FL_AE4A      String?  @db.Char(1)
  FL_AE4B      String?  @db.Char(1)
  FL_AE4C      String?  @db.Char(1)
  FL_AE4D      String?  @db.Char(1)
  FL_AE4E      String?  @db.Char(1)
  FL_AE4F      String?  @db.Char(1)
  FL_AE4G      String?  @db.Char(1)
  FL_AE5       String?  @db.Char(1)
  FL_AE6       String?  @db.Char(1)
  FL_AE7       String?  @db.Char(1)
  FL_AE8       String?  @db.Char(1)

  @@id([LEAID, YEAR])
}

model email_template {
  email_template_id   Int                           @id @default(autoincrement())
  recipient_type      email_template_recipient_type @default(college)
  sender_type         email_template_sender_type    @default(athlete)
  title               String                        @db.VarChar(255)
  subject             String?                       @db.VarChar(255)
  explanation         String?                       @db.MediumText
  content             String                        @db.LongText
  user_id             Int                           @default(0)
  date_created        DateTime                      @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified       DateTime                      @default(now()) @db.Timestamp(0)
  hidden              email_template_hidden         @default(n)
  sports              String?                       @db.VarChar(100)
  mailing_template_id Int?
  send_parents        email_template_send_parents?  @default(n)
  admin_email_sent    admin_email_sent[]

  @@index([date_created], map: "date_created")
  @@index([date_modified], map: "date_modified")
  @@index([user_id, date_created], map: "user_and_date_created")
  @@index([user_id, date_modified], map: "user_and_date_modified")
  @@index([user_id, title(length: 4)], map: "user_and_title")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model email_template_new {
  email_template_new_id Int                             @id @default(autoincrement())
  date_modified         DateTime                        @default(now()) @db.Timestamp(0)
  date_created          DateTime?                       @default(now()) @db.Timestamp(0)
  date_deleted          DateTime?                       @db.Timestamp(0)
  users_id              Int?
  entity_id             Int?
  entity_type           email_template_new_entity_type?
  from                  String                          @db.VarChar(75)
  title                 String                          @db.VarChar(255)
  subject               String                          @db.VarChar(255)
  body                  String                          @db.LongText
  reply_to              String?                         @db.VarChar(255)
  bcc                   String?                         @db.VarChar(255)
  description           String?                         @db.VarChar(300)
  athlete_task          athlete_task[]
  college_task          college_task[]

  @@index([entity_id], map: "entity_id")
  @@index([users_id], map: "users_id")
}

model email_verification {
  email_verification_id Int                       @id @default(autoincrement())
  email                 String                    @db.VarChar(75)
  entity                email_verification_entity @default(request)
  entity_id             Int?
  date_created          DateTime                  @default(now()) @db.Timestamp(0)
  date_approved         DateTime?                 @db.Timestamp(0)
  hash                  String?                   @unique(map: "hash") @db.VarChar(36)
  code                  String?                   @db.VarChar(10)
  code_expired          DateTime?                 @db.DateTime(0)

  @@index([code], map: "code")
  @@index([code_expired], map: "code_expired")
  @@index([date_approved], map: "date_approved")
  @@index([email], map: "email")
  @@index([entity_id], map: "entity_id")
}

model event_owner {
  event_owner_id    Int                 @id @default(autoincrement())
  owner_name        String              @db.VarChar(255)
  owner_short_name  String?             @db.VarChar(10)
  information       String?             @db.VarChar(2000)
  date_created      DateTime?           @default(now()) @db.Timestamp(0)
  date_modified     DateTime?           @default(now()) @db.Timestamp(0)
  event_owner_sport event_owner_sport[]
  tournament        tournament[]
}

model event_owner_sport {
  event_owner_sport_id                                                          Int             @id @default(autoincrement())
  event_owner_id                                                                Int
  default_roster_provider_id                                                    Int?
  default_schedule_provider_id                                                  Int?
  sports_id                                                                     Int
  date_created                                                                  DateTime?       @default(now()) @db.Timestamp(0)
  date_modified                                                                 DateTime?       @default(now()) @db.Timestamp(0)
  event_provider_event_owner_sport_default_roster_provider_idToevent_provider   event_provider? @relation("event_owner_sport_default_roster_provider_idToevent_provider", fields: [default_roster_provider_id], references: [event_provider_id], onUpdate: NoAction, map: "default_roster_provider_id")
  event_provider_event_owner_sport_default_schedule_provider_idToevent_provider event_provider? @relation("event_owner_sport_default_schedule_provider_idToevent_provider", fields: [default_schedule_provider_id], references: [event_provider_id], onUpdate: NoAction, map: "default_schedule_provider_id")
  event_owner                                                                   event_owner     @relation(fields: [event_owner_id], references: [event_owner_id], onDelete: Cascade, onUpdate: NoAction, map: "event_owner_id")
  tournament                                                                    tournament[]

  @@index([default_roster_provider_id], map: "default_roster_provider_id_idx")
  @@index([default_schedule_provider_id], map: "default_schedule_provider_id_idx")
  @@index([event_owner_id], map: "event_owner_id_idx")
}

model event_provider {
  event_provider_id                                                                Int                 @id @default(autoincrement())
  provider_name                                                                    String              @db.VarChar(255)
  provider_code                                                                    String?             @db.VarChar(15)
  sports_ids                                                                       String?             @db.VarChar(200)
  is_roster_provider                                                               Boolean?
  is_schedule_provider                                                             Boolean?
  url                                                                              String?             @db.VarChar(150)
  date_created                                                                     DateTime?           @default(now()) @db.Timestamp(0)
  date_modified                                                                    DateTime?           @default(now()) @db.Timestamp(0)
  event_owner_sport_event_owner_sport_default_roster_provider_idToevent_provider   event_owner_sport[] @relation("event_owner_sport_default_roster_provider_idToevent_provider")
  event_owner_sport_event_owner_sport_default_schedule_provider_idToevent_provider event_owner_sport[] @relation("event_owner_sport_default_schedule_provider_idToevent_provider")
  tournament_tournament_roster_provider_idToevent_provider                         tournament[]        @relation("tournament_roster_provider_idToevent_provider")
  tournament_tournament_schedule_provider_idToevent_provider                       tournament[]        @relation("tournament_schedule_provider_idToevent_provider")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model export_log {
  export_log_id      Int      @id @default(autoincrement())
  college_program_id Int
  users_id           Int
  ip                 String?  @default("") @db.VarChar(40)
  export_name        String   @db.VarChar(127)
  preset             String?  @db.VarChar(127)
  params             String?  @db.MediumText
  row_count          Int?
  date_created       DateTime @default(now()) @db.Timestamp(0)

  @@index([users_id], map: "users_id")
}

model export_preset {
  export_preset_id   Int      @id @default(autoincrement())
  college_program_id Int
  export_name        String   @db.VarChar(127)
  preset             String   @default("default") @db.VarChar(127)
  exportto           String?  @db.VarChar(64)
  exportset          String?  @db.VarChar(4096)
  exportsetall       String?  @db.VarChar(4096)
  date_created       DateTime @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified      DateTime @default(now()) @db.Timestamp(0)

  @@unique([college_program_id, export_name, preset], map: "looking_for_program_export_preset")
}

model game_flags {
  game_flags_id      Int       @id @default(autoincrement())
  shedule_match_id   Int
  tournament_id      Int
  college_program_id Int
  users_id           Int
  flag               Boolean?
  date_modified      DateTime? @default(now()) @db.Timestamp(0)
  date_created       DateTime? @db.Timestamp(0)

  @@index([users_id], map: "users_id")
}

model game_video {
  game_video_id    Int       @id @default(autoincrement())
  date_modified    DateTime? @default(now()) @db.Timestamp(0)
  tournament_id    Int?
  shedule_match_id Int
  title            String?   @db.VarChar(255)
  provider_url     String    @db.VarChar(500)
  url              String?   @db.VarChar(300)
  date_created     DateTime? @db.Timestamp(0)
  date_deleted     DateTime? @db.Timestamp(0)
  upload_start     DateTime? @db.Timestamp(0)
  upload_end       DateTime? @db.Timestamp(0)
  upload_status    String?   @db.VarChar(10)

  @@index([shedule_match_id], map: "shedule_match_id")
  @@index([tournament_id], map: "tournament_id")
}

model game_video_athlete {
  game_video_athlete_id Int       @id @default(autoincrement())
  game_video_id         Int
  athlete_master_id     Int
  date_modified         DateTime? @default(now()) @db.Timestamp(0)
  date_deleted          DateTime? @db.Timestamp(0)

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([game_video_id], map: "game_video_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model history_event {
  history_event_id   Int                   @id @default(autoincrement())
  owner              history_event_owner   @default(athlete)
  owner_user_id      Int?
  athlete_master_id  Int
  column             String                @db.VarChar(100)
  value              String                @default("") @db.VarChar(255)
  old_value          String?               @default("") @db.VarChar(255)
  event              history_event_event?  @default(changed)
  event_const        String?               @default("ATHLETE_PROFILE_CHANGED") @db.VarChar(255)
  object             history_event_object? @default(athlete)
  section            String?               @default("profile") @db.VarChar(255)
  source             String?               @default("") @db.VarChar(255)
  college_program_id Int?
  tournament_id      Int?
  date_created       DateTime              @default(now()) @db.Timestamp(0)

  @@index([athlete_master_id], map: "amid")
  @@index([column], map: "column")
  @@index([college_program_id], map: "cpid")
  @@index([date_created], map: "date_created")
  @@index([event_const(length: 191)], map: "event_const")
  @@index([section(length: 191)], map: "section")
  @@index([tournament_id], map: "tid")
  @@index([owner_user_id], map: "users_id")
}

model import_ml_process {
  import_ml_process_id Int       @id @default(autoincrement())
  date                 DateTime? @db.DateTime(0)
  process_id           Int?
  time                 DateTime? @db.Time(0)
}

model import_process {
  import_process_id Int       @id @default(autoincrement())
  tournament_id     Int
  name              String    @db.VarChar(100)
  stage             Int?      @default(0) @db.SmallInt
  settings          String?   @db.VarChar(8200)
  date_created      DateTime  @default(now()) @db.Timestamp(0)
  date_finished     DateTime? @db.Timestamp(0)
  date_killed       DateTime? @db.Timestamp(0)
  killed_reason     String?   @db.VarChar(4095)

  @@index([stage], map: "stage")
  @@index([tournament_id], map: "tournament_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model import_richkern {
  import_richkern_id  Int                                  @id @default(autoincrement())
  date_modified       DateTime                             @default(now()) @db.Timestamp(0)
  date_created        DateTime                             @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  first               String                               @db.VarChar(40)
  last                String                               @db.VarChar(40)
  gradyear            Int?                                 @db.SmallInt
  division            String                               @db.VarChar(25)
  position            String?                              @db.VarChar(15)
  height              String?                              @db.VarChar(10)
  high_school         String?                              @db.VarChar(100)
  college_name        String?                              @db.VarChar(255)
  city                String?                              @db.VarChar(50)
  state               String?                              @db.VarChar(2)
  team_name           String?                              @db.VarChar(100)
  status              import_richkern_status?              @default(new)
  athlete_master_id   Int?
  athlete_has_changes import_richkern_athlete_has_changes?
  scholarship_status  import_richkern_scholarship_status
  descr               String?                              @db.VarChar(300)

  @@index([athlete_has_changes], map: "athlete_has_changes")
  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([college_name(length: 191)], map: "college_name")
  @@index([descr(length: 191), scholarship_status], map: "description+schol_status")
  @@index([first], map: "first")
  @@index([last], map: "last")
}

model log_athlete_newsletter {
  log_athlete_newsletter_id Int                                 @id @default(autoincrement())
  athlete_master_id         Int
  first                     String?                             @db.VarChar(40)
  last                      String?                             @db.VarChar(40)
  email                     String                              @db.VarChar(75)
  owner_type                log_athlete_newsletter_owner_type?  @default(EMPTY_ENUM_VALUE)
  has_account               log_athlete_newsletter_has_account? @default(n)
  date_created              DateTime?                           @default(now()) @db.Timestamp(0)
  status                    String?                             @db.VarChar(15)

  @@index([email], map: "email")
}

model log_athlete_personal {
  log_athlete_personal_id Int      @id @default(autoincrement())
  users_id                Int
  field                   String   @db.VarChar(45)
  value                   String   @db.MediumText
  date_created            DateTime @default(now()) @db.Timestamp(0)
}

model log_athlete_service_registration {
  log_athlete_service_registration_id Int       @id @default(autoincrement())
  email                               String    @db.VarChar(75)
  service_name                        String    @db.VarChar(45)
  request_url                         String    @db.VarChar(255)
  params                              String    @db.VarChar(1024)
  response                            String    @db.VarChar(500)
  ip                                  String?   @default("") @db.VarChar(40)
  users_id                            Int?
  date_created                        DateTime? @default(now()) @db.Timestamp(0)

  @@index([email], map: "email")
  @@index([service_name], map: "service_name")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_college_membership {
  log_college_membership_id Int       @id @default(autoincrement())
  date_created              DateTime? @db.Timestamp(0)
  date_modified             DateTime  @default(now()) @db.Timestamp(0)
  college_program_id        Int
  old_tier                  String?   @default("") @db.VarChar(2)
  old_tier_expires          DateTime? @db.Timestamp(0)
  new_tier                  String    @db.VarChar(2)
  new_tier_expires          DateTime? @db.Timestamp(0)
  user_id                   Int
  item_id                   Int

  @@index([date_created], map: "lcm_dc")
  @@index([old_tier], map: "lcm_old_tier")
}

model log_cp3_download_db {
  log_cp3_download_db_id Int      @id @default(autoincrement())
  college_program_id     Int
  tournament_id          Int
  users_id               Int
  college_name           String?  @db.VarChar(255)
  sports_id              Int?     @db.SmallInt
  date_requested         DateTime @default(now()) @db.Timestamp(0)

  @@index([college_program_id], map: "cpid")
  @@index([date_requested], map: "date_requested")
  @@index([sports_id], map: "sports_id")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "users_id")
}

model log_cp3_export_evals {
  log_cp3_export_evals_id Int      @id @default(autoincrement())
  college_program_id      Int
  tournament_id           Int
  username                String?  @db.VarChar(75)
  hash                    String?  @db.VarChar(36)
  date_created            DateTime @default(now()) @db.Timestamp(0)

  @@index([college_program_id], map: "cpid")
  @@index([date_created], map: "dc")
  @@index([tournament_id], map: "tid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_frontrush_athlete {
  log_frontrush_athlete_id Int       @id @default(autoincrement())
  athlete_master_id        Int
  first                    String?   @db.VarChar(40)
  last                     String?   @db.VarChar(40)
  email                    String?   @db.VarChar(75)
  gradyear                 Int?      @db.SmallInt
  fr_coach_api_token       String    @db.VarChar(20)
  users_id                 Int?
  request                  String?   @db.MediumText
  response                 String?   @db.VarChar(10000)
  date_request             DateTime? @default(now()) @db.Timestamp(0)
  status                   String?   @db.VarChar(15)
  tournament_id            Int?
  college_program_id       Int?

  @@index([users_id], map: "Index_1")
  @@index([status], map: "Index_2")
  @@index([fr_coach_api_token], map: "Index_3")
  @@index([date_request], map: "Index_4")
  @@index([college_program_id], map: "lfa_college_program_id")
  @@index([tournament_id], map: "lfa_tournament_id")
}

model log_import {
  log_import_id            Int                 @id @default(autoincrement())
  roster_source_ath_row_id String              @db.VarChar(50)
  athlete_master_id        Int?
  tournament_id            Int
  first                    String              @db.VarChar(40)
  last                     String              @db.VarChar(40)
  message                  String              @db.VarChar(512)
  priority                 log_import_priority @default(notice)
  usav_id                  String?             @db.VarChar(30)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_outbox_email {
  log_outbox_email_id Int      @id @default(autoincrement())
  date_sent           DateTime @default(now()) @db.Timestamp(0)
  subject             String   @db.VarChar(255)
  to                  String   @db.VarChar(150)
  from                String   @db.VarChar(75)
  body                String?  @db.LongText
  sender_type         String?  @db.VarChar(15)
  sender_id           Int?
  email_type          String?  @db.VarChar(30)
  message_id          String?  @db.VarChar(100)

  @@index([date_sent], map: "Index_1")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_uar_ajax_requests {
  log_uar_ajax_requests_id Int      @id @default(autoincrement())
  cpid                     Int?
  date_created             DateTime @default(now()) @db.Timestamp(0)
  type                     String?  @db.VarChar(15)
  rows_count               Int?
  length                   Int?
  params                   String?  @db.MediumText
  IP                       String?  @db.VarChar(40)
  user_agent               String?  @db.MediumText
  additional_info          String?  @db.MediumText
  device_hash              String?  @db.VarChar(36)

  @@index([date_created], map: "Index_1")
  @@index([cpid], map: "cpid")
  @@index([device_hash, cpid], map: "hash+cpid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_uar_eval_note {
  log_uar_eval_note_id Int       @id @default(autoincrement())
  date_uploaded        DateTime  @default(now()) @db.Timestamp(0)
  device_time          String?   @db.VarChar(64)
  college_program_id   Int?
  app_version          String?   @db.VarChar(32)
  username             String?   @db.VarChar(75)
  tournament_id        Int?
  athlete_master_id    Int?
  coach                String?   @db.VarChar(2)
  title                String?   @db.VarChar(255)
  note                 String?   @db.VarChar(512)
  device_reg           String?   @db.VarChar(32)
  device_hash          String?   @db.VarChar(36)
  date_modified        DateTime? @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model log_uar_eval_rank {
  log_uar_eval_rank_id Int       @id @default(autoincrement())
  date_uploaded        DateTime  @default(now()) @db.Timestamp(0)
  device_time          String?   @db.VarChar(64)
  college_program_id   Int?
  app_version          String?   @db.VarChar(32)
  username             String?   @db.VarChar(75)
  tournament_id        Int?
  athlete_master_id    Int?
  coach                String?   @db.VarChar(2)
  tag_heart            String?   @db.VarChar(1)
  tag_diamond          String?   @db.VarChar(1)
  tag_club             String?   @db.VarChar(1)
  tag_spade            String?   @db.VarChar(1)
  tag_omit             String?   @db.VarChar(1)
  rank                 String?   @db.VarChar(7)
  device_reg           String?   @db.VarChar(32)
  device_hash          String?   @db.VarChar(36)
  done                 String?   @db.VarChar(1)
  tag_star             Int       @default(0) @db.TinyInt
  date_modified        DateTime? @db.Timestamp(0)

  @@index([date_uploaded], map: "date_uploaded")
}

model log_xml_rpc_requests {
  log_xml_rpc_requests_id Int      @id @default(autoincrement())
  date_request            DateTime @default(now()) @db.Timestamp(0)
  api_key                 String   @db.VarChar(45)
  username                String   @db.VarChar(75)
  college_program_id      Int
  method_name             String   @default("") @db.VarChar(45)
  ip                      String?  @default("") @db.VarChar(40)

  @@index([college_program_id], map: "college_program_id")
  @@index([date_request], map: "date_request")
  @@index([api_key], map: "lxrr_api_key")
}

model login_request {
  login_request_id Int                  @id @default(autoincrement())
  ip_address       String               @db.VarChar(40)
  date_requested   DateTime             @default(now()) @db.Timestamp(0)
  email            String               @db.VarChar(52)
  entity           login_request_entity
  entity_id        Int

  @@index([email], map: "email")
  @@index([entity_id], map: "entity_id")
}

model mail_turn {
  mail_turn_id        Int       @id @default(autoincrement())
  email_id            Int
  headers_email       String?   @db.MediumText
  body_email          String?   @db.MediumText
  status              String?   @db.VarChar(15)
  date_created        DateTime? @db.Timestamp(0)
  date_processing     DateTime? @db.Timestamp(0)
  date_end_processing DateTime? @db.Timestamp(0)
  attachments         String?   @db.MediumText
  message_id          String?   @db.VarChar(100)

  @@index([date_created], map: "date_created")
  @@index([message_id], map: "message_id")
}

model mailing_block {
  mailing_block_id   Int                 @id @default(autoincrement())
  college_program_id Int
  users_id           Int
  date_created       DateTime            @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified      DateTime            @default(now()) @db.Timestamp(0)
  title              String              @db.VarChar(255)
  content            String              @db.LongText
  preview            String?             @db.MediumText
  json               String?             @db.LongText
  type               mailing_block_type?

  @@index([college_program_id], map: "cpid")
  @@index([users_id], map: "users_id")
}

model mailing_group {
  mailing_group_id    Int                @id @default(autoincrement())
  college_program_id  Int
  date_added          DateTime           @default(now()) @db.Timestamp(0)
  date_sent           DateTime?          @db.Timestamp(0)
  name                String             @db.VarChar(100)
  mailing_template_id Int
  count_recipients    Int                @default(0) @db.TinyInt
  type                mailing_group_type @default(EMPTY_ENUM_VALUE)

  @@index([college_program_id], map: "cpid")
  @@index([mailing_template_id], map: "tid")
}

model mailing_recipient {
  mailing_recipient_id Int       @id @default(autoincrement())
  athlete_master_id    Int?      @default(0)
  mailing_template_id  Int
  college_category_id  Int?      @default(0)
  college_program_id   Int
  date_created         DateTime  @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified        DateTime  @default(now()) @db.Timestamp(0)
  first                String?   @db.VarChar(40)
  last                 String?   @db.VarChar(40)
  email                String    @db.VarChar(255)
  staff_id             Int?      @default(0)
  reply_to             Boolean?  @default(false)
  title                String    @db.VarChar(255)
  subject              String    @db.VarChar(255)
  content              String    @db.LongText
  sent                 DateTime? @db.Timestamp(0)
  date_opened          DateTime? @db.Timestamp(0)
  date_replied         DateTime? @db.Timestamp(0)
  add_note             Boolean?  @default(false)
  note_pda             Boolean?  @default(false)
  mailing_group_id     Int?      @default(0)
  date_send            DateTime? @db.Timestamp(0)
  email_queue_id       Int?

  @@index([athlete_master_id], map: "amid")
  @@index([college_category_id], map: "cid")
  @@index([college_program_id], map: "cpid")
  @@index([mailing_group_id], map: "gid")
  @@index([email_queue_id], map: "qid")
  @@index([staff_id], map: "sid")
  @@index([mailing_template_id], map: "tid")
}

model mailing_template {
  mailing_template_id Int                        @id @default(autoincrement())
  college_program_id  Int
  date_created        DateTime                   @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified       DateTime                   @default(now()) @db.Timestamp(0)
  staff_id            Int?                       @default(0)
  reply_to            Boolean?                   @default(false)
  title               String                     @db.VarChar(255)
  subject             String                     @db.VarChar(255)
  content             String                     @db.LongText
  preview_url         String?                    @db.LongText
  active              mailing_template_active    @default(y)
  hide                mailing_template_hide      @default(n)
  camp_info           mailing_template_camp_info @default(n)
  template_json       String?                    @db.LongText
  add_note            Boolean?                   @default(false)
  note_pda            Boolean?                   @default(false)
  is_basic            Boolean?                   @default(false)
  is_system           mailing_template_is_system @default(n)

  @@index([college_program_id], map: "cpid")
  @@index([staff_id], map: "sid")
}

model ml_import_statistique {
  ml_import_statistique_id Int  @id @default(autoincrement())
  import_process_id        Int?
  import_ml_process_id     Int?
  flag_of_insert           Int? @db.TinyInt
  len_provider             Int?
  len_result               Int?
  count_0                  Int?
  count_1                  Int?
  count_2                  Int?
  count_found_ai_id        Int?
  count_sql_automatch      Int?
  count_dismatched         Int?
  count_automatched        Int?
  count_show               Int?
}

model ml_model_check {
  ml_model_check_id    Int     @id @default(autoincrement())
  model_name           String? @db.VarChar(100)
  import_ml_process_id Int?
  athlete_import_id    Int?
  athlete_master_id    Int?
  predict_proba        Int?
  predict              Int?
  model_predict        String? @db.VarChar(100)
  import_result        Int?
  unmatched            Int?
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ncsa_api_requests {
  athlete_or_parent  String? @db.VarChar(20)
  event_id_or_name   String? @db.VarChar(50)
  athlete_first_name String? @db.VarChar(50)
  athlete_last_name  String? @db.VarChar(50)
  athlete_email      String? @db.VarChar(75)
  athlete_phone      String? @db.VarChar(20)
  parent_first_name  String? @db.VarChar(50)
  parent_last_name   String? @db.VarChar(50)
  parent_email       String? @db.VarChar(75)
  parent_phone       String? @db.VarChar(20)
  sport_id           Int?
  graduation_year    Int?
  postal_code        String? @db.VarChar(20)
  funnel_enabled     String? @db.VarChar(1)
  created_at         String? @db.VarChar(15)

  @@ignore
}

model ncsa_request_log {
  ncsa_request_log_id Int                            @id @default(autoincrement())
  request_type        ncsa_request_log_request_type?
  request_uri         String?                        @db.Text
  request_method      String?                        @db.VarChar(10)
  request_body        String?                        @db.Text
  response_code       Int?
  response_body       String?                        @db.Text
  athlete_master_id   Int?
  ncsa_client_id      Int?
  date_created        DateTime?                      @default(now()) @db.Timestamp(0)
  date_modified       DateTime?                      @default(now()) @db.Timestamp(0)

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([date_created], map: "date_created")
  @@index([ncsa_client_id], map: "ncsa_client_id")
}

model nsca_partner {
  ncsa_event_id Int     @id @default(autoincrement())
  partner       String  @db.VarChar(255)
  sport_name    String? @db.VarChar(50)
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model previous_team_roster {
  team_roster_id            Int                                       @default(0)
  date_modified             DateTime                                  @default(now()) @db.Timestamp(0)
  team_master_id            Int?                                      @default(1)
  old_roster_teams_id       Int                                       @default(0)
  tournament_id             Int
  roster_source_id          Int                                       @default(0)
  roster_source_provider    String                                    @default("") @db.VarChar(4)
  roster_source_club_row_id String                                    @db.VarChar(50)
  roster_source_team_row_id String?                                   @db.VarChar(50)
  roster_source_last_update DateTime?                                 @db.Timestamp(0)
  roster_source_status      previous_team_roster_roster_source_status @default(EMPTY_ENUM_VALUE)
  club_roster_id            Int?
  organization_code         String?                                   @db.VarChar(19)
  team_name                 String                                    @default("") @db.VarChar(100)
  schedule_name             String?                                   @db.VarChar(50)
  age                       Int                                       @db.TinyInt
  rank                      String?                                   @db.VarChar(1)
  division                  String?                                   @db.VarChar(75)
  bracket                   String?                                   @db.VarChar(25)
  pool                      String?                                   @db.VarChar(16)
  seed                      Int?                                      @db.SmallInt
  placement                 Boolean?
  state                     String?                                   @db.VarChar(2)
  zip                       String?                                   @db.VarChar(10)
  chapter                   String?                                   @db.VarChar(50)
  uni_color                 String?                                   @db.VarChar(50)
  uni_reverse               String?                                   @default("n") @db.VarChar(1)
  flag                      String?                                   @db.MediumText
  deleted                   Boolean                                   @default(false)
  date_created              Int
  combined_tournament_id    Int?
  locked_row                previous_team_roster_locked_row           @default(n)
  locked_columns            String?                                   @db.VarChar(511)

  @@ignore
}

model priorities {
  priorities_id Int                       @id @default(autoincrement())
  name          String                    @db.VarChar(100)
  priority_type priorities_priority_type?
  athlete_task  athlete_task[]
  college_task  college_task[]
}

model purchase_cart {
  purchase_cart_id         Int                            @id @default(autoincrement())
  date_modified            DateTime                       @default(now()) @db.Timestamp(0)
  cloak                    Int                            @unique(map: "cloak_unique")
  purchase_entity          String                         @db.VarChar(25)
  purchase_entity_id       Int
  purchase_entity_name     String                         @db.VarChar(255)
  purchase_person_fname    String?                        @default("") @db.VarChar(50)
  purchase_person_lname    String?                        @default("") @db.VarChar(50)
  purchase_address         String?                        @default("") @db.VarChar(255)
  purchase_city            String?                        @default("") @db.VarChar(50)
  purchase_state           String?                        @default("") @db.VarChar(30)
  purchase_zip             String?                        @default("") @db.VarChar(15)
  purchase_phone           String?                        @default("") @db.VarChar(15)
  purchase_email           String?                        @default("") @db.VarChar(255)
  purchase_pay_method      String?                        @default("") @db.VarChar(255)
  purchase_pay_ref         String?                        @db.VarChar(50)
  stripe_charge_id         String?                        @db.VarChar(50)
  tilled_payment_intent_id String?                        @db.VarChar(50)
  purchase_date            DateTime                       @db.DateTime(0)
  purchase_total           Decimal?                       @db.Decimal(10, 2)
  purchase_comment         String?                        @db.MediumText
  purchase_detail          String?                        @db.MediumText
  college_user_detail      String?                        @db.VarChar(2000)
  purchase_status          purchase_cart_purchase_status? @default(EMPTY_ENUM_VALUE)
  purchase_step            String?                        @default("") @db.VarChar(5)
  purchase_error           String?                        @db.VarChar(500)

  @@index([purchase_email(length: 191)], map: "purchase_email")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model purchase_completed {
  purchase_completed_id Int                          @id @default(autoincrement())
  date_modified         DateTime                     @default(now()) @db.Timestamp(0)
  date_purchased        DateTime                     @db.Timestamp(0)
  purchase_cart_id      Int
  order_id              Int
  entity                String                       @db.VarChar(25)
  entity_id             Int
  item_id               Int
  item_price            Decimal?                     @db.Decimal(10, 2)
  item_price_full       Decimal?                     @db.Decimal(10, 2)
  note                  String?                      @default("") @db.VarChar(512)
  paid                  String                       @default("n") @db.VarChar(1)
  free                  String                       @default("n") @db.VarChar(1)
  cancelled             purchase_completed_cancelled @default(n)
  checked_in            Int?                         @default(0)
  college_user_id       Int?
  scanned_at            DateTime?                    @db.Timestamp(0)
  scanner_id            String?                      @db.VarChar(60)
  scanner_location      String?                      @db.VarChar(60)
  refund                purchase_completed_refund?   @default(n)
  amount_refunded       Decimal?                     @db.Decimal(10, 2)
  discount              Decimal?                     @db.Decimal(10, 2)
  discount_text         String?                      @db.VarChar(255)

  @@index([cancelled], map: "cancelled")
  @@index([cancelled, paid, free, refund], map: "cancelled_paid_free_refund")
  @@index([college_user_id], map: "college_user_id")
  @@index([entity_id, entity, date_purchased], map: "entity")
  @@index([free], map: "free")
  @@index([item_id], map: "item_id")
  @@index([order_id], map: "order_id")
  @@index([paid], map: "paid")
  @@index([paid, cancelled], map: "paid_cancelled")
  @@index([purchase_cart_id], map: "purchase_cart_id")
  @@index([refund], map: "refund")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model purchase_item {
  purchase_item_id       Int                                   @id @default(autoincrement())
  date_modified          DateTime                              @default(now()) @db.Timestamp(0)
  qualified_group        purchase_item_qualified_group
  sport_id               Int                                   @db.SmallInt
  item_type              purchase_item_item_type
  member_level           purchase_item_member_level?
  tier                   purchase_item_tier?
  year                   String?                               @db.VarChar(4)
  group_level            purchase_item_group_level             @default(Any)
  scottcarter_code       String?                               @db.VarChar(4)
  item_id                Int                                   @unique(map: "item_id")
  tournament_id          Int?                                  @default(0)
  item_description       String                                @db.MediumText
  item_text              String?                               @db.MediumText
  category               String?                               @default("") @db.VarChar(50)
  item_price             Decimal?                              @db.Decimal(10, 2)
  ua_price_cut           Decimal?                              @db.Decimal(10, 2)
  showcase_base_price    String?                               @db.MediumText
  showcase_2nd_price     String?                               @db.MediumText
  tier1_discount         Int?
  dependancy             Int?                                  @default(0)
  has_dependant          Int?                                  @db.TinyInt
  is_dependant           Int?                                  @default(0) @db.TinyInt
  sale_item_for          Int?
  member_required        Int?                                  @default(0) @db.TinyInt
  available_for          purchase_item_available_for?
  display_order          Int?                                  @default(0)
  display_now            Int?                                  @default(0) @db.SmallInt
  display_date           DateTime?                             @db.Timestamp(0)
  admin_display          Int?                                  @default(0) @db.SmallInt
  valid_date             String?                               @db.VarChar(100)
  priority               Int?                                  @default(0)
  devices                Int?                                  @default(0)
  desktops               Int?                                  @default(0)
  has_membership         String?                               @db.VarChar(1)
  show_at_quick_purchase purchase_item_show_at_quick_purchase? @default(n)

  @@index([item_type, year, item_id], map: "item_type+year+item_id")
  @@index([qualified_group], map: "qualified_group")
  @@index([tournament_id], map: "tournaments_id")
}

model request_restore_password {
  request_restore_password_id Int       @id @default(autoincrement())
  email                       String    @db.VarChar(75)
  ip                          String?   @default("") @db.VarChar(40)
  user_agent                  String?   @db.VarChar(512)
  date_created                DateTime? @default(now()) @db.Timestamp(0)

  @@index([email], map: "email")
  @@index([ip], map: "ip")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model result_ml_import {
  result_ml_import_id  Int            @id @default(autoincrement())
  athlete_import_id    Int
  athlete_master_id    Int?
  cozin_distance       Float?         @db.Float
  provider             String?        @db.MediumText
  master               String?        @db.MediumText
  list_ml              String?        @db.MediumText
  probability          Float?         @db.Float
  system_note          String?        @db.VarChar(50)
  target               Int?           @db.TinyInt
  import_ml_process_id Int?
  athlete_import       athlete_import @relation(fields: [athlete_import_id], references: [athlete_import_id], onDelete: Cascade, onUpdate: NoAction, map: "result_ml_import_ibfk_1")

  @@index([athlete_import_id], map: "athlete_import_id")
  @@index([athlete_master_id], map: "athlete_master_id")
}

model roles {
  roles_id      Int         @id @default(autoincrement())
  role          String?     @db.VarChar(30)
  alias         String      @db.MediumText
  note          String?     @db.VarChar(512)
  sort_order    Int?        @db.TinyInt
  date_modified DateTime    @default(now()) @db.Timestamp(0)
  type          roles_type?

  @@index([roles_id], map: "roles_roles_id_idxref")
}

model saved_search {
  saved_search_id    Int                  @id @default(autoincrement())
  date_created       DateTime?            @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  date_modified      DateTime             @default(now()) @db.Timestamp(0)
  college_program_id Int
  mode               String?              @default("") @db.VarChar(100)
  type               String?              @default("") @db.VarChar(15)
  label              String               @db.VarChar(70)
  data               String               @db.VarChar(10000)
  recent             saved_search_recent? @default(y)

  @@index([college_program_id], map: "college_program_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model scheduler_court_map {
  scheduler_court_map_id     Int                      @id @default(autoincrement())
  tournament_id              Int?
  date_created               DateTime?                @default(now()) @db.Timestamp(0)
  date_modified              DateTime?                @default(now()) @db.Timestamp(0)
  pattern                    String?                  @db.VarChar(255)
  offset_value               Int?
  start_number               Int?
  out_prefix                 String?                  @db.VarChar(255)
  scheduler_court_summary_id Int?
  scheduler_court_summary    scheduler_court_summary? @relation(fields: [scheduler_court_summary_id], references: [scheduler_court_summary_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_scheduler_court_summary")
  tournament                 tournament?              @relation(fields: [tournament_id], references: [tournament_id], onDelete: NoAction, onUpdate: NoAction, map: "scheduler_court_map_ibfk_1")

  @@index([scheduler_court_summary_id], map: "fk_scheduler_court_summary")
  @@index([tournament_id], map: "tournament_id")
}

model scheduler_court_summary {
  scheduler_court_summary_id Int                   @id @default(autoincrement())
  tournament_id              Int
  location_name              String                @db.VarChar(255)
  count                      Int
  out_prefix                 String?               @db.VarChar(50)
  offset                     Int
  date_created               DateTime?             @default(now()) @db.Timestamp(0)
  date_modified              DateTime?             @default(now()) @db.Timestamp(0)
  scheduler_court_map        scheduler_court_map[]
  tournament                 tournament            @relation(fields: [tournament_id], references: [tournament_id], onDelete: Cascade, onUpdate: NoAction, map: "fk_tournament")

  @@index([tournament_id], map: "fk_tournament")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model scheduler_log {
  scheduler_log_id Int                           @id @default(autoincrement())
  date_created     DateTime?                     @default(now()) @db.Timestamp(0)
  date_modified    DateTime?                     @default(now()) @db.Timestamp(0)
  start_datetime   DateTime?                     @db.Timestamp(0)
  end_datetime     DateTime?                     @db.Timestamp(0)
  tournament_id    Int?
  parsing_status   scheduler_log_parsing_status?
  error_message    String?                       @db.VarChar(255)
  match_count      String?                       @db.VarChar(255)
  unknown_count    Int?
  extra_count      Int?
  schedule_length  String?                       @db.VarChar(255)
  unknown_teams    Json?

  @@index([tournament_id], map: "tournament_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model scheduler_team_map {
  scheduler_team_map_id Int         @id @default(autoincrement())
  tournament_id         Int?
  date_created          DateTime?   @default(now()) @db.Timestamp(0)
  date_modified         DateTime?   @default(now()) @db.Timestamp(0)
  team_master_id        Int?
  provider_team_id      String?     @db.VarChar(255)
  provider_div          String?     @db.VarChar(255)
  provider_team_div_id  String?     @db.VarChar(255)
  provider_team_name    String?     @db.VarChar(255)
  provider_club_name    String?     @db.VarChar(255)
  tournament            tournament? @relation(fields: [tournament_id], references: [tournament_id], onDelete: NoAction, onUpdate: NoAction, map: "scheduler_team_map_ibfk_1")

  @@index([tournament_id], map: "tournament_id")
}

model scheduler_unknown_team {
  scheduler_unknown_team_id Int         @id @default(autoincrement())
  tournament_id             Int?
  provider_team_id          String?     @db.VarChar(255)
  provider_team_name        String?     @db.VarChar(255)
  provider_div              String?     @db.VarChar(255)
  date_created              DateTime?   @default(now()) @db.Timestamp(0)
  date_modified             DateTime?   @default(now()) @db.Timestamp(0)
  provider_club_name        String?     @db.VarChar(255)
  tournament                tournament? @relation(fields: [tournament_id], references: [tournament_id], onDelete: Cascade, onUpdate: NoAction, map: "scheduler_unknown_team_ibfk_1")

  @@index([tournament_id], map: "tournament_id")
}

model sendgrid_event {
  sendgrid_event_id Int     @id @default(autoincrement())
  email             String  @db.VarChar(255)
  date_created      Int
  ip                String? @default("") @db.VarChar(40)
  sg_message_id     String  @db.VarChar(100)
  useragent         String? @db.VarChar(300)
  event             String  @db.VarChar(30)
  sg_params         String? @db.VarChar(1024)
  ua_params         String? @db.VarChar(512)
  ua_email_id       Int?

  @@index([date_created], map: "date_created")
  @@index([email(length: 191)], map: "email")
  @@index([sg_message_id], map: "sg_message_id")
  @@index([ua_email_id], map: "ua_email_id")
}

model sequelizemeta {
  name String @id @db.VarChar(100)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model service_token {
  service_token_id Int                         @id @default(autoincrement())
  service_name     service_token_service_name? @default(EMPTY_ENUM_VALUE)
  entity           service_token_entity
  entity_id        Int
  provider_user_id String?                     @db.VarChar(255)
  token            String                      @db.VarChar(100)
  date_created     DateTime?                   @default(now()) @db.Timestamp(0)
  active           service_token_active?       @default(n)

  @@index([entity_id], map: "entity_id")
  @@index([provider_user_id(length: 191)], map: "p_user_id")
  @@index([token], map: "token")
}

model session {
  id       String  @id @default("") @db.VarChar(36)
  modified Int?
  lifetime Int?
  data     String? @db.MediumText
}

model shedule_match {
  shedule_match_id Int                   @id @default(autoincrement())
  event            Int                   @default(0) @db.UnsignedInt
  match_id         String                @db.VarChar(36)
  div              String                @default("") @db.VarChar(50)
  day              Int                   @default(0) @db.SmallInt
  date_time        DateTime              @db.Timestamp(0)
  sort_order       String?               @default("0") @db.VarChar(16)
  court            String                @db.VarChar(16)
  pool             String?               @default("") @db.VarChar(16)
  team_1_id        Int?                  @default(0)
  team_2_id        Int?                  @default(0)
  ref_team_id      Int?                  @default(0)
  team_1_name      String?               @db.VarChar(128)
  team_2_name      String?               @db.VarChar(128)
  ref_name         String?               @db.VarChar(128)
  winning_team     Int?                  @default(0) @db.TinyInt
  scores           String?               @db.VarChar(64)
  date_modified    DateTime?             @default(now()) @db.Timestamp(0)
  team1_roster_id  Int?
  team2_roster_id  Int?
  ref_roster_id    Int?
  status           shedule_match_status? @default(updated)
  match_uuid       String?               @db.VarChar(255)
  deleted          DateTime?             @db.Timestamp(0)

  @@unique([event, match_id], map: "event_match_id")
  @@index([date_time], map: "date_time")
  @@index([deleted], map: "deleted")
  @@index([event], map: "event")
  @@index([event, date_modified], map: "event_and_date_modified")
  @@index([event, day, team_1_id], map: "event_day_team1")
  @@index([match_id], map: "match_id")
  @@index([team_1_id], map: "team_id_1")
  @@index([team_2_id], map: "team_id_2")
  @@index([team_1_id, team_2_id], map: "teams_id")
}

model sphinx_update {
  counter_id Int      @id
  t_name     String?  @db.VarChar(256)
  t_last_id  Int
  t_modified DateTime @default(now()) @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sports {
  sports_id     Int              @id @db.SmallInt
  sport         String           @db.VarChar(30)
  short         String           @db.VarChar(6)
  gender        sports_gender    @default(F)
  published     sports_published @default(N)
  date_modified DateTime         @default(now()) @db.Timestamp(0)
}

model states_nearby {
  states_near_id Int      @id @default(autoincrement())
  the_state      String?  @db.VarChar(2)
  near_state     String?  @db.VarChar(2)
  date_modified  DateTime @default(now()) @db.Timestamp(0)

  @@index([near_state, the_state], map: "near_and_state")
  @@index([the_state, near_state], map: "state_and_near")
}

model stream_account_team {
  stream_account_team_id Int    @id @default(autoincrement())
  email                  String @db.VarChar(100)
  account_id             String @unique(map: "acc") @db.VarChar(45)
  team_master_id         Int?
  tournament_id          Int?

  @@unique([team_master_id, account_id], map: "account")
  @@index([email], map: "email")
  @@index([tournament_id], map: "tournament_id")
}

model support_requests {
  support_request_id Int      @id @default(autoincrement())
  fname              String   @db.VarChar(40)
  lname              String   @db.VarChar(40)
  email              String   @db.VarChar(255)
  position           String   @db.VarChar(15)
  comments           String   @db.MediumText
  serialized_content String   @db.MediumText
  reviewed           String   @default("n") @db.VarChar(1)
  hide               String   @default("n") @db.VarChar(1)
  date_submitted     DateTime @db.Timestamp(0)
  lastupdate         DateTime @default(now()) @db.Timestamp(0)
  entity             String?  @db.VarChar(15)
  answer             String?  @db.MediumText
  answered           String?  @db.VarChar(1)
  sent               String?  @db.VarChar(1)
  users_id           Int?
  attach             String?  @db.VarChar(50)

  @@index([email(length: 191)], map: "email")
  @@index([reviewed, support_request_id], map: "reviewed+id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model system_vocabulary {
  system_vocabulary_id Int                       @id @default(autoincrement())
  vocabulary           String                    @db.VarChar(30)
  date_modified        DateTime                  @default(now()) @db.Timestamp(0)
  value                String                    @db.MediumText
  sort_order           Int?                      @db.TinyInt
  id                   Int?
  users_id             Int?
  enabled              system_vocabulary_enabled @default(y)
}

model ta_access {
  ta_access_id       Int              @id @default(autoincrement())
  athlete_master_id  Int
  users_id           Int
  ta_access_level_id Int?
  role               ta_access_role?
  date_created       DateTime?        @default(now()) @db.Timestamp(0)
  date_modified      DateTime?        @default(now()) @db.Timestamp(0)
  date_deleted       DateTime?        @db.Timestamp(0)
  ta_access_level    ta_access_level? @relation(fields: [ta_access_level_id], references: [ta_access_level_id], onUpdate: NoAction, map: "taa_access_level_id")
  athlete_master     athlete_master   @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "taa_athlete_master_id")
  users              users            @relation(fields: [users_id], references: [users_id], onDelete: Cascade, onUpdate: NoAction, map: "taa_users_id")

  @@index([ta_access_level_id], map: "taa_access_level_id")
  @@index([athlete_master_id], map: "taa_athlete_master_id")
  @@index([users_id], map: "taa_users_id")
}

model ta_access_invitation {
  ta_access_invitation_id Int                          @id @default(autoincrement())
  athlete_master_id       Int
  ta_access_level_id      Int?
  email                   String                       @db.VarChar(75)
  role                    ta_access_invitation_role
  name                    String?                      @db.VarChar(100)
  status                  ta_access_invitation_status?
  date_created            DateTime?                    @default(now()) @db.Timestamp(0)
  date_approved           DateTime?                    @db.Timestamp(0)
  date_deleted            DateTime?                    @db.Timestamp(0)
  date_resend             DateTime?                    @db.Timestamp(0)
  ta_access_level         ta_access_level?             @relation(fields: [ta_access_level_id], references: [ta_access_level_id], onUpdate: NoAction, map: "taai_access_level_id")
  athlete_master          athlete_master               @relation(fields: [athlete_master_id], references: [athlete_master_id], onDelete: Cascade, onUpdate: NoAction, map: "taai_athlete_master_id")

  @@index([ta_access_level_id], map: "taai_access_level_id")
  @@index([athlete_master_id], map: "taai_athlete_master_id")
  @@index([email], map: "taai_email")
}

model ta_access_level {
  ta_access_level_id   Int                    @id @default(autoincrement())
  name                 String                 @db.VarChar(100)
  date_created         DateTime?              @default(now()) @db.Timestamp(0)
  ta_access            ta_access[]
  ta_access_invitation ta_access_invitation[]
}

model team_flag {
  team_flag_id       Int       @id @default(autoincrement())
  date_created       DateTime  @db.Timestamp(0)
  date_modified      DateTime  @default(now()) @db.Timestamp(0)
  college_program_id Int
  team_master_id     Int
  tournament_id      Int
  c1_flag            Int?      @default(0)
  c2_flag            Int?      @default(0)
  c3_flag            Int?      @default(0)
  c4_flag            Int?      @default(0)
  c5_flag            Int?      @default(0)
  c1_dm              DateTime? @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  c2_dm              DateTime? @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  c3_dm              DateTime? @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  c4_dm              DateTime? @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  c5_dm              DateTime? @default(dbgenerated("'0000-00-00 00:00:00'")) @db.Timestamp(0)
  users_id           Int?

  @@unique([tournament_id, college_program_id, team_master_id, users_id], map: "event+program+team+users_id")
  @@index([college_program_id], map: "cpid")
  @@index([date_modified], map: "dm")
  @@index([tournament_id, college_program_id], map: "event+program")
  @@index([team_master_id], map: "team_id")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model team_import {
  team_import_id            Int                              @id @default(autoincrement())
  import_process_id         Int
  invalid_data              team_import_invalid_data         @default(y)
  roster_action             team_import_roster_action        @default(ignore)
  team_roster_id            Int?
  team_master_id            Int?
  tournament_id             Int
  found_master_dupl         team_import_found_master_dupl    @default(n)
  matched_by                String?                          @db.VarChar(155)
  match_variety_ids         String?                          @db.VarChar(255)
  roster_source_club_row_id String                           @db.VarChar(50)
  roster_source_team_row_id String                           @db.VarChar(50)
  roster_source_status      team_import_roster_source_status @default(EMPTY_ENUM_VALUE)
  organization_code         String?                          @default("") @db.VarChar(19)
  team_name                 String?                          @db.VarChar(100)
  age                       Int?                             @db.TinyInt
  rank                      String?                          @db.VarChar(1)
  division                  String?                          @db.VarChar(75)
  state                     String?                          @db.VarChar(2)
  zip                       String?                          @db.VarChar(10)
  roster_source_last_update DateTime                         @default(now()) @db.Timestamp(0)
  preparsed_data            String?                          @db.VarChar(1023)
  lock_roster_columns       String?                          @db.VarChar(511)
  imported_invalid_values   String?                          @db.VarChar(1023)

  @@index([team_master_id], map: "Index_1")
  @@index([found_master_dupl], map: "Index_2")
  @@index([import_process_id], map: "import_process_id")
  @@index([invalid_data], map: "invalid_data")
  @@index([roster_action], map: "roster_action")
  @@index([roster_source_club_row_id], map: "roster_source_club_row_id")
  @@index([roster_source_last_update], map: "roster_source_last_update")
  @@index([roster_source_team_row_id], map: "roster_source_team_row_id")
  @@index([team_roster_id], map: "team_roster_id")
  @@index([tournament_id], map: "tournament_id")
}

model team_note {
  team_note_id       Int       @id @default(autoincrement())
  uuid               String    @unique(map: "uuid_UNIQUE") @db.VarChar(36)
  team_master_id     Int
  tournament_id      Int?      @default(0)
  college_program_id Int
  users_id           Int?
  last               Boolean?  @default(true)
  note               String?   @db.MediumText
  date_modified      DateTime? @default(now()) @db.Timestamp(0)
  date_created       DateTime? @db.Timestamp(0)

  @@index([users_id], map: "users_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model tmp_ncsa_request_log {
  ncsa_request_log_id Int                             @default(0)
  ncsa_event_id       String?                         @db.LongText
  event_name          String?                         @db.LongText
  long_event_name     String?                         @db.LongText
  athlete_master_id   Int?
  ncsa_client_id      Int?
  first               String?                         @db.VarChar(40)
  last                String?                         @db.VarChar(40)
  email               String?                         @db.VarChar(75)
  request             String?                         @db.Text
  response            String?                         @db.Text
  response_code       Int?
  date_created        DateTime?                       @default(now()) @db.Timestamp(0)
  tournament_id       Int?
  athlete_roster_id   Int?
  lead_type           tmp_ncsa_request_log_lead_type?

  @@index([athlete_master_id], map: "amid")
  @@index([athlete_roster_id], map: "arid")
  @@index([date_created], map: "date_created")
  @@index([response_code], map: "response_code")
  @@index([tournament_id], map: "tid")
  @@ignore
}

model tournament_coach_access {
  tournament_coach_access_id Int       @id @default(autoincrement())
  tournament_id              Int
  users_id                   Int
  admin_user_id              Int?
  date_added                 DateTime? @default(now()) @db.Timestamp(0)

  @@index([tournament_id], map: "tournament_id")
  @@index([users_id], map: "users_id")
}

model tournament_director {
  tournament_director_id Int     @id @default(autoincrement())
  first                  String  @db.VarChar(40)
  last                   String  @db.VarChar(40)
  email                  String  @db.VarChar(75)
  users_id               Int?    @unique(map: "users_id")
  first1                 String? @db.VarChar(40)
  last1                  String? @db.VarChar(40)
  email1                 String? @db.VarChar(75)
  first2                 String? @db.VarChar(40)
  last2                  String? @db.VarChar(40)
  email2                 String? @db.VarChar(75)
  first3                 String? @db.VarChar(40)
  last3                  String? @db.VarChar(40)
  email3                 String? @db.VarChar(75)

  @@index([email], map: "email")
  @@index([email1], map: "email1")
  @@index([email2], map: "email2")
  @@index([email3], map: "email3")
}

model tournament_location {
  tournament_location_id Int       @id @default(autoincrement())
  tournament_id          Int
  name                   String    @db.VarChar(100)
  short_name             String?   @db.VarChar(10)
  address                String?   @db.VarChar(200)
  city                   String?   @db.VarChar(50)
  state                  String?   @db.VarChar(2)
  zip                    String?   @db.VarChar(10)
  courts_from            Int?
  courts_to              Int?
  date_modified          DateTime? @default(now()) @db.Timestamp(0)
  date_created           DateTime? @db.Timestamp(0)

  @@index([tournament_id], map: "tournament_id")
}

model tournament_user {
  tournament_user_id Int       @id @default(autoincrement())
  tournament_id      Int
  college_program_id Int
  users_id           Int
  date_loaded        DateTime? @default(now()) @db.Timestamp(0)
  app_version        String?   @db.VarChar(32)
  device_type        String?   @db.VarChar(32)

  @@index([college_program_id, tournament_id], map: "tournament_college_program_tournament_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tshl_video {
  tshl_video_id     Int                    @id @default(autoincrement())
  users_id          Int
  athlete_master_id Int
  team_master_id    Int?
  code              String                 @db.VarChar(20)
  title             String                 @db.VarChar(255)
  duration          Int
  thumbnail         String?                @db.VarChar(250)
  date_uploaded     DateTime?              @db.Timestamp(0)
  type              tshl_video_type
  replaced_by       Int
  replacement       tshl_video_replacement @default(n)
  deleted           Int                    @db.TinyInt
  date_modified     DateTime               @default(now()) @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model uar4_sync {
  uar4_sync_id       Int                  @id @default(autoincrement())
  date_modified      DateTime?            @default(now()) @db.Timestamp(0)
  tournament_id      Int
  college_program_id Int
  athlete_master_id  Int
  users_id           Int?
  synced_at          BigInt
  created_at         String?              @db.VarChar(13)
  updated_at         BigInt?
  deleted_at         String?              @db.VarChar(13)
  table_name         String               @db.VarChar(25)
  data               String               @db.MediumText
  app_version        String?              @db.VarChar(10)
  app_type           String?              @db.VarChar(5)
  device_id          String?              @db.VarChar(45)
  device_type        String?              @db.VarChar(32)
  processed          DateTime?            @db.Timestamp(0)
  is_synced          uar4_sync_is_synced? @default(EMPTY_ENUM_VALUE)

  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([date_modified], map: "dm")
  @@index([processed], map: "processed")
  @@index([synced_at], map: "synced_at")
  @@index([table_name], map: "table_name")
  @@index([tournament_id], map: "tid")
  @@index([users_id], map: "users_id")
}

model uar_courts {
  uar_courts_id Int      @id @default(autoincrement())
  event_id      Int
  date_modified DateTime @default(now()) @db.Timestamp(0)
  date_created  DateTime @db.Timestamp(0)
  data          String?  @db.LongText
  file          String?  @db.LongText

  @@index([event_id], map: "event_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model uar_sync {
  uar_sync_id        Int                  @id @default(autoincrement())
  tournament_id      Int?
  college_program_id Int
  athlete_master_id  Int?
  team_master_id     Int?
  coach              Boolean
  type               String?              @db.VarChar(15)
  date_modified      DateTime?            @default(now()) @db.Timestamp(0)
  fbdm               BigInt?
  processed          DateTime?            @db.Timestamp(0)
  app_version        String?              @db.VarChar(32)
  device_type        String?              @db.VarChar(32)
  device_hash        String?              @db.VarChar(36)
  data_source        uar_sync_data_source
  data               String?              @db.Text
  users_name         String?              @db.VarChar(75)
  users_id           Int                  @default(0)
  status             String?              @db.VarChar(15)

  @@index([tournament_id, college_program_id, athlete_master_id, coach], map: "tid_cpid_amid_coach")
  @@index([tournament_id, type, data_source], map: "tid_type_source")
  @@index([fbdm], map: "uar_sync_fbdm_index")
  @@index([processed], map: "uar_sync_processed_index")
  @@index([college_program_id, tournament_id, users_id], map: "uar_sync_program_torun_user_index")
}

model uar_version {
  uar_version_id Int                   @id @default(autoincrement())
  version        String                @db.VarChar(32)
  device_os      uar_version_device_os @default(ios)
  date_release   DateTime              @db.Timestamp(0)
  date_created   DateTime              @default(now()) @db.Timestamp(0)
  description    String?               @db.MediumText

  @@unique([version, device_os], map: "uar_version_version_and_os_uindex")
}

model update_hash_email_tracking {
  update_hash_email_tracking_id Int                                       @id @default(autoincrement())
  athlete_master_update_hash_id Int?
  email                         String                                    @db.VarChar(75)
  email_status                  String?                                   @db.VarChar(15)
  email_type                    String?                                   @db.VarChar(15)
  last_login                    DateTime?                                 @db.Timestamp(0)
  date_edited                   DateTime?                                 @db.Timestamp(0)
  is_lead_date                  DateTime?                                 @db.Timestamp(0)
  is_lead_value                 update_hash_email_tracking_is_lead_value?
  date_modified                 DateTime?                                 @default(now()) @db.Timestamp(0)
  date_created                  DateTime?                                 @default(now()) @db.Timestamp(0)
  athlete_master_update_hash    athlete_master_update_hash?               @relation(fields: [athlete_master_update_hash_id], references: [athlete_master_update_hash_id], onUpdate: NoAction, map: "amuh_athlete_master_update_hash_id")

  @@index([athlete_master_update_hash_id, email_type], map: "uhet_amuh_type")
}

model update_roster_hash {
  update_roster_hash_id Int       @id @default(autoincrement())
  users_id              Int
  hash                  String    @unique(map: "hash") @db.VarChar(36)
  tournament_id         Int?
  club_master_id        Int?
  team_master_id        Int?
  athlete_hash          String    @unique(map: "athlete_hash_UNIQUE") @db.VarChar(36)
  date_modified         DateTime? @default(now()) @db.Timestamp(0)
  date_created          DateTime? @default(now()) @db.Timestamp(0)
  club_staff_master_id  Int?
  email_status          String?   @db.VarChar(15)
  date_seen             DateTime? @db.Timestamp(0)
  coach_log             String?   @db.Text
  code                  String?   @db.VarChar(10)
  code_expired          DateTime? @db.DateTime(0)
  athlete_log           String?   @db.Text

  @@index([club_master_id], map: "club_master_id")
  @@index([team_master_id], map: "team_master_id")
  @@index([tournament_id], map: "tournament_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model usa_ntdp_athletes {
  usa_ntdp_athletes_id Int       @id @default(autoincrement())
  athlete_master_id    Int
  college_program_id   Int
  users_id             Int
  note                 String?   @db.Text
  date_created         DateTime? @default(now()) @db.Timestamp(0)

  @@index([athlete_master_id], map: "amid")
  @@index([college_program_id], map: "cpid")
  @@index([date_created], map: "date_created")
  @@index([users_id], map: "uid")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_hash {
  user_hash_id  Int            @id @default(autoincrement())
  users_id      Int
  entity_id     Int?
  type          user_hash_type
  hash          String?        @db.VarChar(36)
  date_modified DateTime       @default(now()) @db.Timestamp(0)

  @@index([hash], map: "Index_1")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_pattern {
  user_pattern_id Int      @id @default(autoincrement())
  name            String?  @db.VarChar(100)
  type            String?  @db.VarChar(15)
  users_id        Int
  comment         String?  @db.VarChar(255)
  date_modified   DateTime @default(now()) @db.Timestamp(0)
  date_created    DateTime @default(now()) @db.Timestamp(0)
  pattern         String?  @db.MediumText

  @@index([type], map: "type")
  @@index([users_id], map: "users_id")
}

model user_request {
  user_request_id         Int                  @id @default(autoincrement())
  ip_address              String?              @db.VarChar(40)
  entity                  user_request_entity?
  sports_id               Int                  @db.SmallInt
  gender                  user_request_gender? @default(EMPTY_ENUM_VALUE)
  club_master_id          Int?
  first                   String?              @db.VarChar(40)
  last                    String?              @db.VarChar(40)
  username                String               @db.VarChar(75)
  password                String               @db.VarChar(32)
  haddress1               String?              @db.VarChar(100)
  hcity                   String?              @db.VarChar(50)
  hstate                  String?              @db.VarChar(2)
  hzip                    String?              @db.VarChar(10)
  phoneh                  String?              @db.VarChar(20)
  phonec                  String?              @db.VarChar(20)
  phonew                  String?              @db.VarChar(20)
  email                   String?              @db.VarChar(75)
  email_alternate         String?              @db.VarChar(75)
  gradyear                Int?                 @db.SmallInt
  soundex_first           String?              @db.VarChar(25)
  soundex_last            String?              @db.VarChar(25)
  metaphone_last          String?              @db.VarChar(16)
  date_requested          DateTime             @default(now()) @db.Timestamp(0)
  level                   Int?                 @db.TinyInt
  match_athlete_master_id Int?
  status                  user_request_status  @default(new)
  serialize               String?              @db.MediumText
  entity_id               Int?

  @@index([email], map: "email")
  @@index([email_alternate], map: "email_alternate")
  @@index([entity, status, user_request_id], map: "entity+status+id")
  @@index([gradyear, club_master_id, hstate, first(length: 2), haddress1, last], map: "search1_key")
  @@index([username], map: "username")
}

model users_photo {
  users_photo_id    Int      @id @default(autoincrement()) @db.UnsignedInt
  users_id          Int      @db.UnsignedInt
  athlete_master_id Int
  url               String?  @db.VarChar(300)
  file_name         String?  @db.VarChar(130)
  comments          String?  @db.VarChar(1000)
  height            Int?     @db.SmallInt
  width             Int?     @db.UnsignedSmallInt
  date_modified     DateTime @default(now()) @db.Timestamp(0)
  sort_order        Int      @db.TinyInt

  @@index([athlete_master_id], map: "athlete_master_id")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model verified_value {
  verified_value_id  Int                    @id @default(autoincrement())
  college_program_id Int
  athlete_master_id  Int
  name               String?                @db.VarChar(100)
  value              String                 @db.VarChar(255)
  date_modified      DateTime               @default(now()) @db.Timestamp(0)
  users_id           Int?
  status             verified_value_status?
  uuid               String?                @unique(map: "uuid_UNIQUE") @db.VarChar(36)
  comment            String?                @db.Text

  @@index([athlete_master_id, verified_value_id], map: "athlete_master_id+verified_value_id")
  @@index([college_program_id, athlete_master_id, name, status], map: "search_for_value")
  @@index([users_id], map: "users_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model wireless_provider {
  wireless_provider_id Int     @id @default(autoincrement())
  name                 String? @db.VarChar(100)
  domain               String  @db.VarChar(255)
  sort_order           Int?    @db.TinyInt
}

enum YesNo {
  y
  n
}

enum Size {
  ANY
  S
  M
  L
  XL
}

enum YesNoEmpty {
  y
  n
  empty @map("")
}

enum Gender {
  F
  M
  empty @map("")
}

enum Handedness {
  L
  R
}

enum LeadType {
  athlete
  parent
  coach_packet
}

enum FilledUpdateRosterForm {
  athlete
  parent
}

enum ImportDivisionsMode {
  and
  only
}

enum Coaches {
  zero @map("0")
  one  @map("1")
  two  @map("2")
}

enum UserType {
  admin
  college
  athlete
  club
  touchstat
  event_owner
  sport_admin
  team_coach
  parent
  recruiter
}

enum AuthorType {
  club
  admin
  athlete
  sport_admin
  college
  team_coach
}

enum ChangeStatus {
  new
  approved
  declined
  locked
  unlocked
  changed
}

enum email_template_recipient_type {
  athlete
  college
  club
}

enum history_event_owner {
  athlete
  admin
  college
  club
  system
}

enum ncsa_request_log_request_type {
  athlete_injection
}

enum service_token_service_name {
  EMPTY_ENUM_VALUE @map("")
  EAPI
  MVPCAST
  BT
}

enum athlete_import_invalid_data {
  y
  n
}

enum athlete_master_archive_archive_type {
  insert
  update
  delete
}

enum club_import_invalid_data {
  y
  n
}

enum club_staff_import_invalid_data {
  y
  n
}

enum email_template_sender_type {
  admin
  athlete
  college
  club
}

enum email_verification_entity {
  athlete
  request
  college
  registration
}

enum priorities_priority_type {
  task
}

enum purchase_item_qualified_group {
  athlete
  club
  college
  parent
  touchstat
}

enum service_token_entity {
  college
  athlete
}

enum team_import_invalid_data {
  y
  n
}

enum uar_version_device_os {
  ios
  android
}

enum user_request_entity {
  athlete
  college
  club
  touchstat
}

enum admin_email_sent_recipient_type {
  athlete
  college
  club
}

enum athlete_import_roster_action {
  create
  update
  delete
  ignore
}

enum club_import_roster_action {
  create
  update
  delete
  ignore
}

enum club_staff_import_roster_action {
  create
  update
  delete
  ignore
}

enum cms_page_site {
  TS
  UA
}

enum college_user_athlete_metrics_has_athlete_tags {
  y
  n
}

enum custom_field_type {
  text
  selectBox
  checkBox
}

enum sports_gender {
  M
  F
  U
}

enum team_import_roster_action {
  create
  update
  delete
  ignore
}

enum user_hash_type {
  evals_export @map("evals-export")
}

enum athlete_email_primary {
  ZERO @map("0")
  ONE  @map("1")
}

enum athlete_email_sent_seen {
  y
  n
}

enum college_athlete_committed_user_type {
  college
  athlete
}

enum college_athlete_follow_status {
  y
  n
}

enum college_questionnaire_sent_recipient {
  athlete
  staff
  director
  coordinator
  parent1
  parent2
  club
}

enum college_sms_update_enabled {
  y
  n
}

enum college_user_athlete_metrics_has_college_notes {
  y
  n
}

enum login_request_entity {
  athlete
  college
  club
}

enum purchase_item_item_type {
  membership
  tournament
  scottcarter
  sale
  license
  schedule
}

enum sports_published {
  Y
  N
}

enum ta_access_role {
  recruiter
  parent
  club
  team_coach
}

enum ta_access_invitation_role {
  recruiter
  parent
  club
  team_coach
}

enum user_request_gender {
  M
  F
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_email_owner {
  athlete
  coach
  director
  parent
  coordinator
  adult
  other
}

enum athlete_request_request_type {
  export
  details
  email
  print
}

enum athlete_team_coach_change_status {
  new
  approved
  declined
}

enum athlete_unsubscribe_promotion {
  y
  n
}

enum college_camp_active {
  y
  n
}

enum college_rank_master_device_type {
  iphone
  palm
  bb
  wm
}

enum college_user_athlete_metrics_has_athlete_ranks {
  y
  n
}

enum log_athlete_newsletter_owner_type {
  athlete
  coach
  director
  parent1
  parent2
  coordinator
  EMPTY_ENUM_VALUE @map("")
}

enum purchase_item_member_level {
  gold
  silver
  silverplus
  EMPTY_ENUM_VALUE @map("")
}

enum admin_mailing_recipients_sent {
  y
  n
}

enum athlete_event_event_type {
  tournament
  camp
  showcase
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_unsubscribe_follow {
  y
  n
}

enum college_athlete_committed_status {
  pending
  approved
  declined
}

enum college_rank_history_device_type {
  iphone
  palm
  bb
  wm
}

enum college_user_active_staff {
  y
  n
}

enum college_user_athlete_metrics_has_evaluation {
  y
  n
}

enum email_template_new_entity_type {
  college
  athlete
  club
  admin
}

enum log_athlete_newsletter_has_account {
  y
  n
}

enum purchase_item_tier {
  // 1 @map("1")
  // 2 @map("2")
  // 3 @map("3")
  EMPTY_ENUM_VALUE @map("")
}

enum roles_type {
  admin
  college
  athlete
  club
  event_owner
  sport_admin
}

enum scheduler_log_parsing_status {
  success
  error
  in_process
  no_updates
}

enum ta_access_invitation_status {
  pending
  approved
  declined
  deleted
}

enum admin_mailing_athletes_sent {
  y
  n
}

enum admin_mailing_club_directors_sent {
  y
  n
}

enum admin_mailing_coaches_sent {
  y
  n
}

enum athlete_email_sent_sent {
  y
  n
}

enum athlete_import_found_master_dupl {
  y
  n
}

enum club_roster_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  old
  new
  updated
  skipped
}

enum cms_page_visible {
  y
  n
}

enum college_category_athlete_done {
  y
  n
}

enum college_rank_archive_device_type {
  iphone
  palm
  bb
  wm
}

enum college_rank_group_general {
  y
  n
}

enum college_task_column_dividers_enabled {
  y
  n
}

enum college_user_athlete_metrics_has_frontrush_log {
  y
  n
}

enum history_event_event {
  added
  changed
  removed
}

enum log_import_priority {
  warning
  error
  info
  notice
}

enum mailing_group_type {
  category
  tag
  google
  email
  athlete
  EMPTY_ENUM_VALUE @map("")
}

enum service_token_active {
  y
  n
}

enum system_vocabulary_enabled {
  y
  n
}

enum team_import_found_master_dupl {
  y
  n
}

enum verified_value_status {
  EMPTY_ENUM_VALUE @map("")
  approved
  declined
  obsolete
}

enum admin_help_type {
  athlete
  college
  club
  all
}

enum club_import_found_master_dupl {
  y
  n
}

enum club_staff_roster_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  old
  new
  updated
  skipped
}

enum college_rank_group_ntdp {
  y
  n
}

enum college_tag_primary {
  y
  n
}

enum college_task_multiple_options {
  y
  n
}

enum college_user_rcv_recruit_email {
  y
  n
}

enum purchase_item_group_level {
  Any
  D1
  D2
  D3
}

enum saved_search_recent {
  y
  n
}

enum update_hash_email_tracking_is_lead_value {
  y
  n
}

enum admin_help_visible {
  y
  n
}

enum athlete_roster_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  old
  new
  updated
  skipped
}

enum athlete_team_coach_change_sent {
  y
  n
}

enum athlete_teammate_unknown_teammate {
  n
  y
}

enum college_category_task {
  y
  n
}

enum college_note_device_type {
  iphone
  palm
  bb
  wm
}

enum history_event_object {
  athlete
  college
  club
}

enum mailing_block_type {
  header
  footer
  EMPTY_ENUM_VALUE @map("")
  content
}

enum tshl_video_type {
  team
  individual
}

enum admin_email_sent_seen {
  y
  n
}

enum admin_help_link_type {
  facebox
  new
}

enum admin_user_unlimited_sport_search {
  y
  n
}

enum athlete_email_sent_answered {
  y
  n
}

enum college_player_needed_active {
  y
  n
}

enum college_tag_tag_type {
  omit
  done
  default
  questionnaire
  basic
  default_volleyball
}

enum email_template_hidden {
  y
  n
}

enum mailing_template_active {
  y
  n
}

enum previous_team_roster_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  old
  new
  updated
  skipped
}

enum team_roster_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  old
  new
  updated
  skipped
}

enum athlete_email_sent_hidden {
  y
  n
}

enum athlete_request_anonymity_status {
  new
  declined
  approved
}

enum club_staff_import_found_master_dupl {
  y
  n
}

enum college_category_done_on_send {
  y
  n
}

enum mailing_template_hide {
  y
  n
}

enum tshl_video_replacement {
  n
  y
}

enum admin_email_sent_sent {
  y
  n
}

enum athlete_teammate_has_account {
  ZERO @map("0")
  ONE  @map("1")
}

enum club_import_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  new
  updated
  old
  skipped
}

enum college_category_remove_on_done {
  y
  n
}

enum college_rank_multiple_per_coach {
  y
  n
}

enum mailing_template_camp_info {
  n
  y
}

enum team_import_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  new
  updated
  old
  skipped
}

enum tournament_showcase_event {
  n
  y
}

enum athlete_video_seen {
  y
  n
}

enum club_staff_import_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  new
  updated
  old
  skipped
}

enum college_rank_is_primary {
  y
  n
}

enum email_template_send_parents {
  y
  n
}

enum purchase_completed_cancelled {
  y
  n
}

enum tournament_free {
  n
  y
}

enum uar_sync_data_source {
  old_uar
  web
  uar
  repair
}

enum import_richkern_status {
  new
  automatched
  matched
  updated
  old
  rejected
}

enum tournament_use_after_date {
  n
  y
}

enum athlete_video_type {
  full
  highlight
  clip
}

enum college_category_is_questionnaire {
  ZERO @map("0")
  ONE  @map("1")
}

enum college_user_cell_phone_verified {
  y
  n
}

enum tmp_ncsa_request_log_lead_type {
  athlete
  parent
  coach_packet
}

enum athlete_import_roster_source_status {
  EMPTY_ENUM_VALUE @map("")
  new
  updated
  old
  skipped
}

enum club_staff_import_gender {
  EMPTY_ENUM_VALUE @map("")
  F
  M
}

enum college_rank_master_done {
  Y
  N
}

enum import_richkern_athlete_has_changes {
  EMPTY_ENUM_VALUE @map("")
  y
  n
}

enum college_rank_history_done {
  Y
  N
}

enum college_task_due_date_template {
  custom
  hours        @map("3_hours")
  tomorrow
  next_week
  next_month
  next_june_15
}

enum import_richkern_scholarship_status {
  U
  S
  V
  EMPTY_ENUM_VALUE @map("")
  Not_a_Senior     @map("Not a Senior")
  C
}

enum mailing_template_is_system {
  y
  n
}

enum uar4_sync_is_synced {
  y
  n
  EMPTY_ENUM_VALUE @map("")
}

enum college_rank_archive_done {
  Y
  N
}

enum tournament_has_alpha_courts {
  n
  y
}

enum college_category_due_date_template {
  hour
  day
  week
  month
}

enum purchase_completed_refund {
  y
  n
}

enum college_program_blocked {
  n
  y
}

enum college_user_used_for_ua {
  y
  n
}

enum college_program_send_sms_updates {
  y
  n
}

enum college_user_show_cell_phone {
  y
  n
}

enum shedule_match_status {
  updated
  old
}

enum college_program_membership {
  free
  silver
  silverplus
  gold
}

enum college_user_show_cell_phone_to_coaches {
  y
  n
}

enum college_note_priority {
  ZERO  @map("0")
  ONE   @map("1")
  TWO   @map("2")
  THREE @map("3")
  FOUR  @map("4")
  FIVE  @map("5")
}

enum purchase_cart_purchase_status {
  pending
  paid
  success
  error
  EMPTY_ENUM_VALUE @map("")
}

enum college_user_show_home_phone {
  y
  n
}

enum athlete_master_high_acad_standard {
  EMPTY_ENUM_VALUE @map("")
  Y
  N
}

enum college_note_done {
  Y
  N
}

enum purchase_item_available_for {
  EMPTY_ENUM_VALUE @map("")
  silver
  silverplus
  gold
}

enum club_roster_hidden {
  y
  n
}

enum college_user_show_work_phone {
  y
  n
}

enum user_request_status {
  new
  created
  declined
  matched
  restored
}

enum athlete_master_archive_gender {
  F
  M
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_user_high_acad_standard {
  EMPTY_ENUM_VALUE @map("")
  Y
  N
}

enum club_roster_locked_row {
  y
  n
}

enum athlete_import_gender {
  EMPTY_ENUM_VALUE @map("")
  F
  M
}

enum athlete_master_scholarship_status {
  EMPTY_ENUM_VALUE @map("")
  U
  S
  V
  Not_a_Senior     @map("Not a Senior")
  C
}

enum athlete_master_archive_high_acad_standard {
  EMPTY_ENUM_VALUE @map("")
  Y
  N
}

enum athlete_import_scholarship_status {
  EMPTY_ENUM_VALUE @map("")
  U
  C
}

enum athlete_roster_gender {
  EMPTY_ENUM_VALUE @map("")
  F
  M
}

enum athlete_master_archive_scholarship_status {
  EMPTY_ENUM_VALUE @map("")
  U
  S
  V
  Not_a_Senior     @map("Not a Senior")
  C
}

enum athlete_roster_high_acad_standard {
  EMPTY_ENUM_VALUE @map("")
  Y
  N
}

enum previous_team_roster_locked_row {
  y
  n
}

enum college_note_source {
  web
  event
  questionnaire
  email
  upload
  summary
}

enum athlete_roster_scholarship_status {
  U
  S
  V
  EMPTY_ENUM_VALUE @map("")
  Not_a_Senior     @map("Not a Senior")
  C
}

enum purchase_item_show_at_quick_purchase {
  y
  n
}

enum college_user_message_notify {
  email
  notify
  sun
  mon
  tue
  wed
  thu
  fri
  sat
  none
}

enum athlete_user_scholarship_status {
  U
  S
  V
  EMPTY_ENUM_VALUE @map("")
  Not_a_Senior     @map("Not a Senior")
  C
}

enum college_user_layout_version {
  EMPTY_ENUM_VALUE @map("")
  ONE   @map("1")
  TWO   @map("2")
  THREE @map("3")
}

enum college_user_recruiter_usa_ntdp {
  y
  n
}

enum athlete_import_handed {
  L
  R
}

enum athlete_import_throws {
  L
  R
}

enum college_user_ntdp_staff {
  y
  n
}

enum college_program_allow_email_templates {
  y
  n
}

enum college_user_is_evaluator {
  y
  n
}

enum athlete_master_archive_has_changes {
  y
  n
}

enum athlete_master_archive_plays_sand_vb {
  y
  n
}

enum athlete_master_archive_anonymity {
  y
  n
}

enum athlete_master_parent1_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_master_archive_usa_ntdp {
  y
  n
}

enum athlete_master_archive_parent1_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_master_parent2_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum college_program_ac_subscription {
  professional
  consumer
  plus
}

enum college_program_allow_pay_by_check {
  y
  n
}

enum athlete_master_archive_parent2_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum college_program_access_only_receipts {
  y
  n
}

enum college_program_usa_ntdp {
  y
  n
}

enum college_program_cross_program_visibility {
  y
  n
}

enum college_program_use_new_profile {
  y
  n
}

enum athlete_master_archive_is_lead {
  y
  n
}

enum college_program_migrate_to_new_profile {
  start
  done
  error
}

enum athlete_user_parent1_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_user_parent2_relationship {
  mother
  father
  stepmother
  stepfather
  friend_of_family @map("friend of family")
  guardian
  sibling
  grandparent
  uncle
  aunt
  other
  EMPTY_ENUM_VALUE @map("")
}

enum athlete_user_sand_considered_usa_ntdp {
  n
  y
}
